"""
Sklearn-style BERT Classifier for Spam Detection
Replaces wandb with pure sklearn training patterns
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import torch
from torch.utils.data import DataLoader, TensorDataset
from torch.optim import Adam<PERSON>
from torch.nn import CrossEntropyLoss
from sklearn.base import BaseEstimator, ClassifierMixin
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import confusion_matrix, classification_report, roc_auc_score
from transformers import BertTokenizer, BertForSequenceClassification
import warnings
warnings.filterwarnings('ignore')


class SklearnBertClassifier(BaseEstimator, ClassifierMixin):
    """BERT Classifier yang kompatibel dengan sklearn"""
    
    def __init__(self, model_name='indobenchmark/indobert-base-p1', 
                 max_length=128, learning_rate=2e-5, batch_size=16, 
                 num_epochs=3, patience=3, min_delta=0.001):
        self.model_name = model_name
        self.max_length = max_length
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.num_epochs = num_epochs
        self.patience = patience
        self.min_delta = min_delta
        
        # Initialize components
        self.tokenizer = None
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Training history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': []
        }
    
    def _initialize_model(self):
        """Initialize tokenizer and model"""
        print(f"Loading {self.model_name}...")
        self.tokenizer = BertTokenizer.from_pretrained(self.model_name)
        self.model = BertForSequenceClassification.from_pretrained(
            self.model_name, num_labels=2
        )
        self.model.to(self.device)
        print(f"Model loaded on {self.device}")
    
    def _tokenize_data(self, texts):
        """Tokenize texts"""
        encodings = self.tokenizer(
            list(texts),
            truncation=True,
            padding=True,
            max_length=self.max_length,
            return_tensors='pt'
        )
        return encodings
    
    def _create_dataloader(self, encodings, labels=None, shuffle=True):
        """Create DataLoader"""
        if labels is not None:
            dataset = TensorDataset(
                encodings['input_ids'],
                encodings['attention_mask'],
                torch.tensor(labels, dtype=torch.long)
            )
        else:
            dataset = TensorDataset(
                encodings['input_ids'],
                encodings['attention_mask']
            )
        
        return DataLoader(dataset, batch_size=self.batch_size, shuffle=shuffle)
    
    def fit(self, X, y, X_val=None, y_val=None, verbose=True):
        """Train the model (sklearn-style fit method)"""
        if self.model is None:
            self._initialize_model()
        
        # Prepare data
        train_encodings = self._tokenize_data(X)
        train_loader = self._create_dataloader(train_encodings, y, shuffle=True)
        
        val_loader = None
        if X_val is not None and y_val is not None:
            val_encodings = self._tokenize_data(X_val)
            val_loader = self._create_dataloader(val_encodings, y_val, shuffle=False)
        
        # Setup optimizer and loss
        optimizer = AdamW(self.model.parameters(), lr=self.learning_rate)
        criterion = CrossEntropyLoss()
        
        # Training loop
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.num_epochs):
            # Training phase
            train_loss, train_acc = self._train_epoch(train_loader, optimizer, criterion)
            self.history['train_loss'].append(train_loss)
            self.history['train_acc'].append(train_acc)
            
            # Validation phase
            if val_loader is not None:
                val_loss, val_acc = self._validate_epoch(val_loader, criterion)
                self.history['val_loss'].append(val_loss)
                self.history['val_acc'].append(val_acc)
                
                if verbose:
                    print(f"Epoch {epoch+1}/{self.num_epochs}:")
                    print(f"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
                    print(f"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
                
                # Early stopping
                if val_loss < best_val_loss - self.min_delta:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= self.patience:
                        print(f"Early stopping at epoch {epoch+1}")
                        break
            else:
                if verbose:
                    print(f"Epoch {epoch+1}/{self.num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
        
        return self
    
    def _train_epoch(self, dataloader, optimizer, criterion):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for batch in dataloader:
            input_ids, attention_mask, labels = [b.to(self.device) for b in batch]
            
            optimizer.zero_grad()
            
            outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
            loss = criterion(outputs.logits, labels)
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            _, predicted = torch.max(outputs.logits.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        return total_loss / len(dataloader), correct / total
    
    def _validate_epoch(self, dataloader, criterion):
        """Validate for one epoch"""
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch in dataloader:
                input_ids, attention_mask, labels = [b.to(self.device) for b in batch]
                
                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
                loss = criterion(outputs.logits, labels)
                
                total_loss += loss.item()
                
                _, predicted = torch.max(outputs.logits.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        return total_loss / len(dataloader), correct / total
    
    def predict(self, X):
        """Make predictions (sklearn-style predict method)"""
        if self.model is None:
            raise ValueError("Model not trained yet. Call fit() first.")
        
        self.model.eval()
        encodings = self._tokenize_data(X)
        dataloader = self._create_dataloader(encodings, shuffle=False)
        
        predictions = []
        
        with torch.no_grad():
            for batch in dataloader:
                if len(batch) == 3:  # Has labels
                    input_ids, attention_mask, _ = [b.to(self.device) for b in batch]
                else:  # No labels
                    input_ids, attention_mask = [b.to(self.device) for b in batch]
                
                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
                _, predicted = torch.max(outputs.logits.data, 1)
                predictions.extend(predicted.cpu().numpy())
        
        return np.array(predictions)
    
    def predict_proba(self, X):
        """Predict class probabilities"""
        if self.model is None:
            raise ValueError("Model not trained yet. Call fit() first.")
        
        self.model.eval()
        encodings = self._tokenize_data(X)
        dataloader = self._create_dataloader(encodings, shuffle=False)
        
        probabilities = []
        
        with torch.no_grad():
            for batch in dataloader:
                if len(batch) == 3:  # Has labels
                    input_ids, attention_mask, _ = [b.to(self.device) for b in batch]
                else:  # No labels
                    input_ids, attention_mask = [b.to(self.device) for b in batch]
                
                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
                probs = torch.softmax(outputs.logits, dim=-1)
                probabilities.extend(probs.cpu().numpy())
        
        return np.array(probabilities)
    
    def plot_training_history(self):
        """Plot training history"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Loss plot
        ax1.plot(self.history['train_loss'], label='Train Loss')
        if self.history['val_loss']:
            ax1.plot(self.history['val_loss'], label='Val Loss')
        ax1.set_title('Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # Accuracy plot
        ax2.plot(self.history['train_acc'], label='Train Acc')
        if self.history['val_acc']:
            ax2.plot(self.history['val_acc'], label='Val Acc')
        ax2.set_title('Training Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.show()


def evaluate_model(classifier, X, y, dataset_name):
    """Evaluate model using sklearn metrics"""
    print(f"\n=== {dataset_name} Evaluation ===")
    
    # Predictions
    y_pred = classifier.predict(X)
    y_prob = classifier.predict_proba(X)[:, 1]  # Probability for class 1
    
    # Calculate metrics
    accuracy = accuracy_score(y, y_pred)
    precision = precision_score(y, y_pred, average='weighted')
    recall = recall_score(y, y_pred, average='weighted')
    f1 = f1_score(y, y_pred, average='weighted')
    auc_score = roc_auc_score(y, y_prob)
    
    print(f"Accuracy:  {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"Precision: {precision:.4f} ({precision*100:.2f}%)")
    print(f"Recall:    {recall:.4f} ({recall*100:.2f}%)")
    print(f"F1-Score:  {f1:.4f} ({f1*100:.2f}%)")
    print(f"AUC Score: {auc_score:.4f} ({auc_score*100:.2f}%)")
    
    # Classification report
    print(f"\nClassification Report ({dataset_name}):")
    print(classification_report(y, y_pred, target_names=['Non-Spam', 'Spam']))
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'auc_score': auc_score,
        'y_pred': y_pred,
        'y_prob': y_prob
    }


if __name__ == "__main__":
    print("Sklearn BERT Classifier - No wandb required!")
    print("Import this module to use SklearnBertClassifier class")
