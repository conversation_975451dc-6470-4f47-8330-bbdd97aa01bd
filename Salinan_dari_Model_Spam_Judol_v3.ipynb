#Install Libraries (Jika di Google Colab)
# !pip install transformers datasets scikit-learn wordcloud matplotlib

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc
from sklearn.metrics import roc_auc_score, precision_recall_curve
from datasets import Dataset
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
import torch
import warnings
warnings.filterwarnings('ignore')

#Load & Preprocess Data
#kolom 'komentar' dan 'label' (0=tidak judol, 1=judol)
df = pd.read_csv('Dataset.csv')
df

# Menampilkan 5 baris pertama untuk melihat sampel data
print("\nSampel Data:")
print(df.head())

# Melihat informasi ringkas (tipe data, nilai non-null)
print("\nInformasi Dataset:")
df.info()


# Informasi umum tentang dataset
print("=== Informasi Dataset ===")
print(f"Jumlah baris: {df.shape[0]}")
print(f"Jumlah kolom: {df.shape[1]}")
print(f"\nNama kolom: {list(df.columns)}")
print(f"\nTipe data:")
print(df.dtypes)
print(f"\nInformasi missing values:")
print(df.isnull().sum())

# Distribusi label dalam bentuk angka
print("=== Distribusi Label ===")
label_counts = df['Label'].value_counts()
print(label_counts)
print(f"\nPersentase:")
print(df['Label'].value_counts(normalize=True) * 100)

# Analisis panjang teks
print("=== Analisis Panjang Teks ===")

# Pastikan kolom Comment ada
if 'Comment' in df.columns:
    # Buat kolom sementara untuk analisis
    df['text_length'] = df['Comment'].str.len()
    df['word_count'] = df['Comment'].str.split().str.len()

    print(f"Statistik panjang karakter:")
    print(df['text_length'].describe())
    print(f"\nStatistik jumlah kata:")
    print(df['word_count'].describe())
else:
    print("❌ Error: Kolom 'Comment' tidak ditemukan!")
    print(f"Kolom yang tersedia: {list(df.columns)}")

# Perbandingan panjang teks berdasarkan label
print("=== Perbandingan Panjang Teks Berdasarkan Label ===")

# Pastikan kolom yang diperlukan ada
if 'text_length' in df.columns and 'word_count' in df.columns:
    print("\nPanjang karakter berdasarkan label:")
    print(df.groupby('Label')['text_length'].describe())
    print("\nJumlah kata berdasarkan label:")
    print(df.groupby('Label')['word_count'].describe())
else:
    print("❌ Error: Kolom 'text_length' atau 'word_count' tidak ditemukan!")
    print("Pastikan cell sebelumnya (Analisis Panjang Teks) sudah dijalankan.")
    print(f"Kolom yang tersedia: {list(df.columns)}")

print(df.columns)

# PENTING: Membersihkan kolom sementara yang dibuat untuk analisis
# Kolom ini hanya diperlukan untuk analisis data understanding
# dan akan dihapus sebelum preprocessing untuk menghindari konflik

print("=== Pembersihan Kolom Sementara ===")
print(f"Kolom sebelum pembersihan: {df.columns.tolist()}")

if 'text_length' in df.columns:
    df = df.drop(['text_length', 'word_count'], axis=1)
    print("✅ Kolom sementara berhasil dihapus: text_length, word_count")
else:
    print("ℹ️  Kolom sementara tidak ditemukan (sudah dihapus atau belum dibuat)")

print(f"Kolom setelah pembersihan: {df.columns.tolist()}")
print(f"\n⚠️  CATATAN: Jika Anda mendapat error 'KeyError: text_length',")
print(f"   pastikan untuk menjalankan cell ini setelah menjalankan")
print(f"   semua cell di bagian Data Understanding.")

#Pastikan semua nama kolom lower case agar konsisten
df.columns = df.columns.str.lower()
print("Nama kolom setelah normalisasi:", df.columns.tolist())

#Cek nama kolom label (asumsi: 'label' dalam huruf kecil)
if 'label' not in df.columns:
    print("Nama kolom label:", df.columns)  # tampilkan semua nama kolom agar bisa dicek
    raise ValueError("Kolom 'label' tidak ditemukan!")

# Mengecek jumlah data duplikat sebelum dihapus
print(f"Jumlah data duplikat sebelum dibersihkan: {df.duplicated().sum()}")

# Menghapus data duplikat
df.drop_duplicates(inplace=True)

print(f"Jumlah data duplikat setelah dibersihkan: {df.duplicated().sum()}")

#Buang baris dengan label kosong (NaN)
initial_count = len(df)
df = df.dropna(subset=['label'])
after_count = len(df)
print(f"Data sebelum: {initial_count}, setelah buang label kosong: {after_count}")

#Split data (stratify harus label tanpa NaN)
train_df, val_df = train_test_split(
    df,
    test_size=0.2,
    stratify=df['label'],
    random_state=42
)

print("=== Informasi Data Splitting ===")
print(f"Jumlah train data: {len(train_df)}")
print(f"Jumlah validasi data: {len(val_df)}")
print(f"\nDistribusi label di training set:")
print(train_df['label'].value_counts(normalize=True) * 100)
print(f"\nDistribusi label di validation set:")
print(val_df['label'].value_counts(normalize=True) * 100)

# Menampilkan contoh komentar spam (label = 1)
print("=== Contoh Komentar SPAM (Label = 1) ===")
spam_samples = df[df['label'] == 1.0]['comment'].head(10)
for i, comment in enumerate(spam_samples, 1):
    print(f"{i}. {comment}")
    print()

# Menampilkan contoh komentar non-spam (label = 0)
print("=== Contoh Komentar NON-SPAM (Label = 0) ===")
non_spam_samples = df[df['label'] == 0.0]['comment'].head(10)
for i, comment in enumerate(non_spam_samples, 1):
    print(f"{i}. {comment}")
    print()

import matplotlib.pyplot as plt
import seaborn as sns

# Set style untuk visualisasi
plt.style.use('default')
sns.set_palette("husl")

# Membuat subplot untuk visualisasi
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. Bar plot distribusi label
sns.countplot(data=df, x='label', ax=axes[0,0])
axes[0,0].set_title('Distribusi Label (Bar Plot)')
axes[0,0].set_xlabel('Label (0=Non-Spam, 1=Spam)')
axes[0,0].set_ylabel('Jumlah')

# 2. Pie chart distribusi label
label_counts = df['label'].value_counts()
axes[0,1].pie(label_counts.values, labels=['Spam', 'Non-Spam'], autopct='%1.1f%%', startangle=90)
axes[0,1].set_title('Distribusi Label (Pie Chart)')

# 3. Histogram panjang teks (jika kolom tersedia)
if 'text_length' in df.columns:
    axes[1,0].hist(df[df['Label']==0]['text_length'], alpha=0.7, label='Non-Spam', bins=30)
    axes[1,0].hist(df[df['Label']==1]['text_length'], alpha=0.7, label='Spam', bins=30)
    axes[1,0].set_title('Distribusi Panjang Teks')
    axes[1,0].set_xlabel('Panjang Karakter')
    axes[1,0].set_ylabel('Frekuensi')
    axes[1,0].legend()
else:
    axes[1,0].text(0.5, 0.5, 'Kolom text_length\ntidak tersedia\n\nJalankan cell analisis\npanjang teks terlebih dahulu',
                   ha='center', va='center', transform=axes[1,0].transAxes, fontsize=10)
    axes[1,0].set_title('Distribusi Panjang Teks (Data Tidak Tersedia)')

# 4. Box plot panjang teks berdasarkan label (jika kolom tersedia)
if 'text_length' in df.columns:
    sns.boxplot(data=df, x='label', y='text_length', ax=axes[1,1])
    axes[1,1].set_title('Box Plot Panjang Teks per Label')
    axes[1,1].set_xlabel('Label (0=Non-Spam, 1=Spam)')
    axes[1,1].set_ylabel('Panjang Karakter')
else:
    axes[1,1].text(0.5, 0.5, 'Kolom text_length\ntidak tersedia\n\nJalankan cell analisis\npanjang teks terlebih dahulu',
                   ha='center', va='center', transform=axes[1,1].transAxes, fontsize=10)
    axes[1,1].set_title('Box Plot Panjang Teks (Data Tidak Tersedia)')

plt.tight_layout()
plt.show()

from collections import Counter
import re

# Fungsi untuk membersihkan teks
def clean_text(text):
    # Mengubah ke lowercase dan menghapus karakter khusus
    text = re.sub(r'[^a-zA-Z\s]', '', text.lower())
    return text

# Menganalisis kata-kata paling umum untuk spam
spam_texts = df[df['label'] == 1.0]['comment'].apply(clean_text)
spam_words = ' '.join(spam_texts).split()
spam_word_freq = Counter(spam_words)

print("=== 20 Kata Paling Umum dalam SPAM ===")
for word, freq in spam_word_freq.most_common(20):
    print(f"{word}: {freq}")

# Menganalisis kata-kata paling umum untuk non-spam
non_spam_texts = df[df['label'] == 0.0]['comment'].apply(clean_text)
non_spam_words = ' '.join(non_spam_texts).split()
non_spam_word_freq = Counter(non_spam_words)

print("=== 20 Kata Paling Umum dalam NON-SPAM ===")
for word, freq in non_spam_word_freq.most_common(20):
    print(f"{word}: {freq}")

# Import library untuk word cloud
from wordcloud import WordCloud
import matplotlib.pyplot as plt

# Fungsi untuk membuat word cloud
def create_wordcloud(text_data, title, max_words=100):
    """
    Membuat word cloud dari data teks

    Parameters:
    text_data: string - teks yang akan dibuat word cloud
    title: string - judul untuk word cloud
    max_words: int - maksimal kata yang ditampilkan
    """
    # Konfigurasi word cloud
    wordcloud = WordCloud(
        width=800,
        height=400,
        background_color='white',
        max_words=max_words,
        colormap='viridis',
        relative_scaling=0.5,
        random_state=42
    ).generate(text_data)

    return wordcloud

# Persiapkan data teks untuk word cloud
print("=== Mempersiapkan Data untuk Word Cloud ===")

# Gabungkan semua teks spam yang sudah dibersihkan
spam_text_combined = ' '.join(spam_texts.dropna())
print(f"Total karakter teks spam: {len(spam_text_combined)}")

# Gabungkan semua teks non-spam yang sudah dibersihkan
non_spam_text_combined = ' '.join(non_spam_texts.dropna())
print(f"Total karakter teks non-spam: {len(non_spam_text_combined)}")

# Buat word cloud untuk spam dan non-spam
print("\n=== Membuat Word Cloud ===")

# Setup figure dengan 2 subplot
fig, axes = plt.subplots(1, 2, figsize=(16, 8))

# Word cloud untuk SPAM
if len(spam_text_combined.strip()) > 0:
    spam_wordcloud = create_wordcloud(spam_text_combined, 'SPAM Comments', max_words=100)
    axes[0].imshow(spam_wordcloud, interpolation='bilinear')
    axes[0].set_title('Word Cloud - SPAM Comments', fontsize=16, fontweight='bold', color='red')
    axes[0].axis('off')
else:
    axes[0].text(0.5, 0.5, 'Tidak ada data SPAM\nyang cukup untuk\nmembuat word cloud',
                ha='center', va='center', transform=axes[0].transAxes, fontsize=12)
    axes[0].set_title('Word Cloud - SPAM Comments (No Data)', fontsize=16)
    axes[0].axis('off')

# Word cloud untuk NON-SPAM
if len(non_spam_text_combined.strip()) > 0:
    non_spam_wordcloud = create_wordcloud(non_spam_text_combined, 'NON-SPAM Comments', max_words=100)
    axes[1].imshow(non_spam_wordcloud, interpolation='bilinear')
    axes[1].set_title('Word Cloud - NON-SPAM Comments', fontsize=16, fontweight='bold', color='green')
    axes[1].axis('off')
else:
    axes[1].text(0.5, 0.5, 'Tidak ada data NON-SPAM\nyang cukup untuk\nmembuat word cloud',
                ha='center', va='center', transform=axes[1].transAxes, fontsize=12)
    axes[1].set_title('Word Cloud - NON-SPAM Comments (No Data)', fontsize=16)
    axes[1].axis('off')

plt.tight_layout()
plt.show()

# Analisis tambahan dari word cloud
print("=== Analisis Word Cloud ===")

# Bandingkan kata-kata paling umum antara spam dan non-spam
print("\n1. PERBANDINGAN KATA PALING UMUM:")
print("\nTop 10 kata SPAM:")
for i, (word, freq) in enumerate(spam_word_freq.most_common(10), 1):
    print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

print("\nTop 10 kata NON-SPAM:")
for i, (word, freq) in enumerate(non_spam_word_freq.most_common(10), 1):
    print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

# Cari kata-kata yang unik untuk masing-masing kategori
spam_words_set = set(spam_word_freq.keys())
non_spam_words_set = set(non_spam_word_freq.keys())

# Kata yang hanya ada di spam
spam_only_words = spam_words_set - non_spam_words_set
# Kata yang hanya ada di non-spam
non_spam_only_words = non_spam_words_set - spam_words_set
# Kata yang ada di keduanya
common_words = spam_words_set & non_spam_words_set

print(f"\n2. DISTRIBUSI KATA:")
print(f"   - Kata unik di SPAM: {len(spam_only_words)}")
print(f"   - Kata unik di NON-SPAM: {len(non_spam_only_words)}")
print(f"   - Kata yang ada di keduanya: {len(common_words)}")
print(f"   - Total kata unik: {len(spam_words_set | non_spam_words_set)}")

# Tampilkan beberapa kata unik untuk masing-masing kategori
if len(spam_only_words) > 0:
    spam_only_sorted = sorted([(word, spam_word_freq[word]) for word in spam_only_words],
                             key=lambda x: x[1], reverse=True)
    print(f"\n3. KATA UNIK SPAM (Top 10):")
    for i, (word, freq) in enumerate(spam_only_sorted[:10], 1):
        print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

if len(non_spam_only_words) > 0:
    non_spam_only_sorted = sorted([(word, non_spam_word_freq[word]) for word in non_spam_only_words],
                                 key=lambda x: x[1], reverse=True)
    print(f"\n4. KATA UNIK NON-SPAM (Top 10):")
    for i, (word, freq) in enumerate(non_spam_only_sorted[:10], 1):
        print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

# Analisis perbedaan frekuensi kata yang sama
if len(common_words) > 0:
    print(f"\n5. PERBEDAAN FREKUENSI KATA UMUM:")
    word_diff = []
    for word in common_words:
        spam_freq = spam_word_freq[word]
        non_spam_freq = non_spam_word_freq[word]
        diff_ratio = spam_freq / non_spam_freq if non_spam_freq > 0 else float('inf')
        word_diff.append((word, spam_freq, non_spam_freq, diff_ratio))

    # Urutkan berdasarkan rasio perbedaan
    word_diff.sort(key=lambda x: x[3], reverse=True)

    print("   Kata yang lebih sering muncul di SPAM:")
    for i, (word, spam_freq, non_spam_freq, ratio) in enumerate(word_diff[:5], 1):
        if ratio > 1:
            print(f"   {i}. {word:<15} - SPAM: {spam_freq:3d}x, NON-SPAM: {non_spam_freq:3d}x (rasio: {ratio:.2f})")

    print("\n   Kata yang lebih sering muncul di NON-SPAM:")
    word_diff.sort(key=lambda x: x[3])  # Urutkan ascending untuk yang lebih sering di non-spam
    for i, (word, spam_freq, non_spam_freq, ratio) in enumerate(word_diff[:5], 1):
        if ratio < 1:
            print(f"   {i}. {word:<15} - SPAM: {spam_freq:3d}x, NON-SPAM: {non_spam_freq:3d}x (rasio: {ratio:.2f})")

print("\n=== INSIGHT DARI WORD CLOUD ===")
print("✅ Word cloud membantu mengidentifikasi:")
print("   1. Kata-kata yang paling dominan dalam setiap kategori")
print("   2. Perbedaan visual antara pola kata spam vs non-spam")
print("   3. Kata-kata unik yang bisa menjadi indikator spam")
print("   4. Distribusi dan frekuensi kata secara visual")
print("\n💡 Informasi ini berguna untuk:")
print("   - Feature engineering (pemilihan kata kunci)")
print("   - Preprocessing (stopwords removal)")
print("   - Model training (understanding data patterns)")

# Word cloud untuk keseluruhan dataset
print("=== Word Cloud Keseluruhan Dataset ===")

# Gabungkan semua teks (spam + non-spam)
all_texts = df['comment'].apply(clean_text)
all_text_combined = ' '.join(all_texts.dropna())

print(f"Total karakter dalam dataset: {len(all_text_combined)}")
print(f"Total kata unik dalam dataset: {len(set(all_text_combined.split()))}")

# Buat word cloud untuk keseluruhan dataset
if len(all_text_combined.strip()) > 0:
    plt.figure(figsize=(12, 8))

    overall_wordcloud = create_wordcloud(all_text_combined, 'Overall Dataset', max_words=150)
    plt.imshow(overall_wordcloud, interpolation='bilinear')
    plt.title('Word Cloud - Keseluruhan Dataset (Spam + Non-Spam)',
              fontsize=18, fontweight='bold', pad=20)
    plt.axis('off')
    plt.tight_layout()
    plt.show()

    print("✅ Word cloud keseluruhan dataset berhasil dibuat!")

    # Statistik tambahan
    all_word_freq = Counter(all_text_combined.split())
    print(f"\n📊 STATISTIK KESELURUHAN:")
    print(f"   - Total kata: {sum(all_word_freq.values())}")
    print(f"   - Kata unik: {len(all_word_freq)}")
    print(f"   - Rata-rata frekuensi per kata: {sum(all_word_freq.values())/len(all_word_freq):.2f}")

    print(f"\n🔝 TOP 15 KATA PALING UMUM (Keseluruhan):")
    for i, (word, freq) in enumerate(all_word_freq.most_common(15), 1):
        percentage = (freq / sum(all_word_freq.values())) * 100
        print(f"   {i:2d}. {word:<15} ({freq:4d}x - {percentage:.2f}%)")

else:
    print("❌ Tidak ada data yang cukup untuk membuat word cloud keseluruhan")

# Mengecek duplikasi data
print("=== Analisis Duplikasi ===")
print(f"Jumlah data duplikat: {df.duplicated().sum()}")
print(f"Jumlah komentar unik: {df['comment'].nunique()}")
print(f"Total data: {len(df)}")

# Mengecek data kosong atau sangat pendek
print("\n=== Analisis Data Kosong/Pendek ===")
empty_comments = df[df['comment'].str.len() < 5]
print(f"Jumlah komentar dengan panjang < 5 karakter: {len(empty_comments)}")

if len(empty_comments) > 0:
    print("Contoh komentar pendek:")
    for comment in empty_comments['Comment'].head():
        print(f"'{comment}'")

# Ringkasan temuan dari data understanding
print("=== RINGKASAN DATA UNDERSTANDING ===")
print(f"\n1. INFORMASI DATASET:")
print(f"   - Total data: {len(df)} komentar")
print(f"   - Jumlah fitur: {df.shape[1]}")
print(f"   - Missing values: {df.isnull().sum().sum()}")

print(f"\n2. DISTRIBUSI LABEL:")
label_dist = df['label'].value_counts(normalize=True) * 100
print(f"   - Spam (1): {label_dist[1.0]:.1f}%")
print(f"   - Non-Spam (0): {label_dist[0.0]:.1f}%")
print(f"   - Dataset relatif seimbang: {'Ya' if abs(label_dist[1.0] - label_dist[0.0]) < 10 else 'Tidak'}")

print(f"\n3. KARAKTERISTIK TEKS:")
if 'text_length' in df.columns and 'word_count' in df.columns:
    print(f"   - Rata-rata panjang karakter: {df['text_length'].mean():.1f}")
    print(f"   - Rata-rata jumlah kata: {df['word_count'].mean():.1f}")
    print(f"   - Panjang teks spam vs non-spam: {'Berbeda signifikan' if abs(df[df['Label']==1]['text_length'].mean() - df[df['Label']==0]['text_length'].mean()) > 10 else 'Relatif sama'}")
else:
    print(f"   - Analisis karakteristik teks belum dilakukan")
    print(f"   - Jalankan cell 'Analisis Panjang Teks' terlebih dahulu")

print(f"\n4. KUALITAS DATA:")
print(f"   - Data duplikat: {df.duplicated().sum()}")
print(f"   - Komentar sangat pendek (<5 karakter): {len(df[df['comment'].str.len() < 5])}")
print(f"   - Kualitas data: {'Baik' if df.duplicated().sum() < len(df)*0.05 and len(df[df['comment'].str.len() < 5]) < len(df)*0.01 else 'Perlu pembersihan'}")

print(f"\n5. REKOMENDASI PREPROCESSING:")
print(f"   - Normalisasi teks (lowercase, remove special chars)")
print(f"   - Tokenisasi dan stemming/lemmatization")
print(f"   - Penanganan kata-kata umum (stopwords)")
print(f"   - Feature engineering (TF-IDF, word embeddings)")
if df.duplicated().sum() > 0:
    print(f"   - Penghapusan data duplikat")
if len(df[df['comment'].str.len() < 5]) > 0:
    print(f"   - Penanganan komentar sangat pendek")

df['label'].value_counts().plot(kind="pie",autopct="%.1f%%")
plt.title("Label")
plt.show()

#Load Tokenizer IndoBERT
print("=== Setup Tokenizer IndoBERT ===")
tokenizer = BertTokenizer.from_pretrained('indobenchmark/indobert-base-p1')
print("✅ IndoBERT tokenizer berhasil dimuat!")
print(f"   - Vocabulary size: {tokenizer.vocab_size}")
print(f"   - Model max length: {tokenizer.model_max_length}")

#Load Model IndoBERT for Classification
model = BertForSequenceClassification.from_pretrained(
    'indobenchmark/indobert-base-p1',
    num_labels=2  # binary classification
)

#Training Arguments & Metric
from transformers import TrainingArguments
training_args = TrainingArguments(
    output_dir='./results',
    eval_strategy='epoch',
    save_strategy='epoch',
    learning_rate=2e-5,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=32,
    num_train_epochs=3,
    weight_decay=0.01,
    logging_dir='./logs',
    logging_steps=20,
    save_total_limit=1,
    load_best_model_at_end=True,
    metric_for_best_model='accuracy'
)


from sklearn.metrics import accuracy_score, f1_score

def compute_metrics(eval_pred):
    logits, labels = eval_pred
    preds = logits.argmax(axis=-1)
    acc = accuracy_score(labels, preds)
    f1 = f1_score(labels, preds)
    return {'accuracy': acc, 'f1': f1}

# Setup Early Stopping untuk mencegah overfitting
from transformers import EarlyStoppingCallback

# Early stopping callback
early_stopping = EarlyStoppingCallback(
    early_stopping_patience=3,  # Stop jika tidak ada improvement dalam 3 evaluasi
    early_stopping_threshold=0.001  # Minimum improvement threshold
)

print("Early stopping callback telah disiapkan")
print("- Patience: 3 evaluasi")
print("- Threshold: 0.001")

# Custom callback untuk monitoring overfitting
from transformers import TrainerCallback
import matplotlib.pyplot as plt

class OverfittingMonitor(TrainerCallback):
    def __init__(self):
        self.train_losses = []
        self.eval_losses = []
        self.eval_accuracies = []
        self.steps = []

    def on_log(self, args, state, control, model=None, logs=None, **kwargs):
        if 'train_loss' in logs:
            self.train_losses.append(logs['train_loss'])
            self.steps.append(state.global_step)

    def on_evaluate(self, args, state, control, model=None, logs=None, **kwargs):
        if logs:
            if 'eval_loss' in logs:
                self.eval_losses.append(logs['eval_loss'])
            if 'eval_accuracy' in logs:
                self.eval_accuracies.append(logs['eval_accuracy'])

            # Cek overfitting
            if len(self.eval_losses) >= 2:
                current_eval_loss = self.eval_losses[-1]
                previous_eval_loss = self.eval_losses[-2]

                if current_eval_loss > previous_eval_loss:
                    print(f"⚠️  WARNING: Validation loss meningkat dari {previous_eval_loss:.4f} ke {current_eval_loss:.4f}")
                    print("   Kemungkinan mulai overfitting!")
                else:
                    print(f"✅ Validation loss turun: {previous_eval_loss:.4f} → {current_eval_loss:.4f}")

    def plot_training_progress(self):
        if len(self.eval_losses) > 0:
            plt.figure(figsize=(12, 4))

            # Plot losses
            plt.subplot(1, 2, 1)
            if len(self.train_losses) > 0:
                plt.plot(self.steps, self.train_losses, label='Training Loss', color='blue')
            if len(self.eval_losses) > 0:
                eval_steps = self.steps[::len(self.steps)//len(self.eval_losses)][:len(self.eval_losses)]
                plt.plot(eval_steps, self.eval_losses, label='Validation Loss', color='red', marker='o')
            plt.xlabel('Steps')
            plt.ylabel('Loss')
            plt.title('Training vs Validation Loss')
            plt.legend()
            plt.grid(True)

            # Plot accuracy
            plt.subplot(1, 2, 2)
            if len(self.eval_accuracies) > 0:
                eval_steps = self.steps[::len(self.steps)//len(self.eval_accuracies)][:len(self.eval_accuracies)]
                plt.plot(eval_steps, self.eval_accuracies, label='Validation Accuracy', color='green', marker='s')
            plt.xlabel('Steps')
            plt.ylabel('Accuracy')
            plt.title('Validation Accuracy')
            plt.legend()
            plt.grid(True)

            plt.tight_layout()
            plt.show()

# Inisialisasi monitor
overfitting_monitor = OverfittingMonitor()
print("Overfitting monitor telah disiapkan")

# Ensure labels are integers
df['label'] = df['label'].astype(int)


# Recreate train/val split with proper labels
train_df, val_df = train_test_split(df, test_size=0.2, stratify=df['label'], random_state=42)

# Rename label column to 'labels' for HuggingFace compatibility
train_df = train_df.rename(columns={'label': 'labels'})
val_df = val_df.rename(columns={'label': 'labels'})

# Create datasets
train_dataset = Dataset.from_pandas(train_df)
val_dataset = Dataset.from_pandas(val_df)

# Apply tokenization
def preprocess_func(examples):
    return tokenizer(
        examples['comment'],
        padding='max_length',
        truncation=True,
        max_length=128
    )

train_dataset = train_dataset.map(preprocess_func, batched=True)
val_dataset = val_dataset.map(preprocess_func, batched=True)

# Set format for PyTorch - Fix NumPy compatibility issue
import torch

# Remove unnecessary columns and set format
train_dataset = train_dataset.remove_columns(['__index_level_0__'] if '__index_level_0__' in train_dataset.column_names else [])
val_dataset = val_dataset.remove_columns(['__index_level_0__'] if '__index_level_0__' in val_dataset.column_names else [])

# Convert labels to proper format
def convert_labels(example):
    example['labels'] = int(example['labels'])
    return example

train_dataset = train_dataset.map(convert_labels)
val_dataset = val_dataset.map(convert_labels)

# Alternative approach - create custom data collator to avoid NumPy issues
from transformers import DataCollatorWithPadding

# Don't set format, let the data collator handle it
data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

print(f"Train dataset size: {len(train_dataset)}")
print(f"Validation dataset size: {len(val_dataset)}")
print(f"Train dataset columns: {train_dataset.column_names}")
print(f"Sample from train dataset: {train_dataset[0]}")

# Create Trainer with Fixed Datasets
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    compute_metrics=compute_metrics,
    callbacks=[early_stopping, overfitting_monitor]
)

# Fix for NumPy compatibility issue
import numpy as np
import warnings

# Suppress the specific NumPy warning
warnings.filterwarnings('ignore', message='Unable to avoid copy while creating an array')

# Alternative: Downgrade numpy if needed (uncomment if the above doesn't work)
# !pip install numpy==1.24.3

print(f"NumPy version: {np.__version__}")
print("NumPy compatibility fix applied")

#Training
trainer.train()

# 8. Save Trained Model
trainer.save_model('./indobert-judol-classifier')
tokenizer.save_pretrained('./indobert-judol-classifier')

# Fungsi untuk mendapatkan prediksi dari model
def get_predictions(trainer, dataset):
    """Mendapatkan prediksi dan probabilitas dari model"""
    predictions = trainer.predict(dataset)
    y_pred = np.argmax(predictions.predictions, axis=1)
    y_prob = torch.softmax(torch.tensor(predictions.predictions), dim=1)[:, 1].numpy()
    return y_pred, y_prob

# Cek nama kolom yang tersedia
print("=== Checking Available Columns ===")
print(f"train_df columns: {train_df.columns.tolist()}")
print(f"val_df columns: {val_df.columns.tolist()}")

# Tentukan nama kolom label yang benar
label_col = None
for col in train_df.columns:
    if col.lower() in ['label', 'labels', 'target', 'y']:
        label_col = col
        break

if label_col is None:
    print("❌ Error: Label column not found!")
    print("Available columns:", train_df.columns.tolist())
    # Asumsikan kolom kedua adalah label jika tidak ditemukan
    label_col = train_df.columns[1]
    print(f"Using column '{label_col}' as label column")

print(f"✅ Using '{label_col}' as label column")

# Mendapatkan prediksi untuk training dan validation set
print("\n=== Mendapatkan Prediksi Model ===")
print("Prediksi untuk training set...")
train_pred, train_prob = get_predictions(trainer, train_dataset)
train_true = train_df[label_col].values.astype(int)

print("Prediksi untuk validation set...")
val_pred, val_prob = get_predictions(trainer, val_dataset)
val_true = val_df[label_col].values.astype(int)

print("✅ Prediksi berhasil diperoleh!")
print(f"Training set: {len(train_pred)} predictions")
print(f"Validation set: {len(val_pred)} predictions")

# Menghitung metrik akurasi untuk training dan validation set
def calculate_metrics(y_true, y_pred, y_prob, dataset_name):
    """Menghitung berbagai metrik evaluasi"""
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='weighted')
    recall = recall_score(y_true, y_pred, average='weighted')
    f1 = f1_score(y_true, y_pred, average='weighted')
    auc_score = roc_auc_score(y_true, y_prob)
    
    print(f"\n=== Metrik {dataset_name} ===")
    print(f"Accuracy:  {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"Precision: {precision:.4f} ({precision*100:.2f}%)")
    print(f"Recall:    {recall:.4f} ({recall*100:.2f}%)")
    print(f"F1-Score:  {f1:.4f} ({f1*100:.2f}%)")
    print(f"AUC Score: {auc_score:.4f} ({auc_score*100:.2f}%)")
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'auc_score': auc_score
    }

# Menghitung metrik untuk training set
train_metrics = calculate_metrics(train_true, train_pred, train_prob, "Training Set")

# Menghitung metrik untuk validation set
val_metrics = calculate_metrics(val_true, val_pred, val_prob, "Validation Set")

# Classification Report Detail
print("\n=== CLASSIFICATION REPORT DETAIL ===")
print("\n--- Training Set ---")
print(classification_report(train_true, train_pred, 
                          target_names=['Non-Spam', 'Spam'],
                          digits=4))

print("\n--- Validation Set ---")
print(classification_report(val_true, val_pred, 
                          target_names=['Non-Spam', 'Spam'],
                          digits=4))

# Cross-Validation menggunakan K-Fold (5-fold)
from sklearn.model_selection import StratifiedKFold
from sklearn.base import BaseEstimator, ClassifierMixin

class BertClassifierWrapper(BaseEstimator, ClassifierMixin):
    """Wrapper untuk BERT classifier agar kompatibel dengan sklearn"""
    
    def __init__(self, model_name='indobenchmark/indobert-base-p1', max_length=128):
        self.model_name = model_name
        self.max_length = max_length
        self.tokenizer = None
        self.model = None
        
    def fit(self, X, y):
        # Untuk cross-validation, kita akan menggunakan model yang sudah dilatih
        # Ini adalah implementasi sederhana untuk demonstrasi
        return self
    
    def predict(self, X):
        # Menggunakan model yang sudah dilatih untuk prediksi
        # Implementasi sederhana menggunakan trainer yang sudah ada
        return np.random.randint(0, 2, len(X))  # Placeholder

# Implementasi Cross-Validation manual untuk BERT
def manual_cross_validation(df, k_folds=5):
    """Melakukan cross-validation manual untuk model BERT"""
    
    skf = StratifiedKFold(n_splits=k_folds, shuffle=True, random_state=42)
    cv_scores = []
    
    print(f"=== {k_folds}-Fold Cross Validation ===")
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(df['comment'], df['label']), 1):
        print(f"\nFold {fold}/{k_folds}:")
        
        # Split data untuk fold ini
        fold_train = df.iloc[train_idx]
        fold_val = df.iloc[val_idx]
        
        print(f"  Training samples: {len(fold_train)}")
        print(f"  Validation samples: {len(fold_val)}")
        
        # Untuk demonstrasi, kita akan menggunakan model yang sudah dilatih
        # untuk mengevaluasi pada fold validation set
        
        # Tokenize validation data untuk fold ini
        fold_val_texts = fold_val['comment'].tolist()
        fold_val_labels = fold_val['label'].tolist()
        
        # Buat dataset untuk fold validation
        fold_val_encodings = tokenizer(fold_val_texts, 
                                     truncation=True, 
                                     padding=True, 
                                     max_length=128,
                                     return_tensors='pt')
        
        fold_val_dataset = Dataset.from_dict({
            'input_ids': fold_val_encodings['input_ids'],
            'attention_mask': fold_val_encodings['attention_mask'],
            'labels': fold_val_labels
        })
        
        # Prediksi menggunakan model yang sudah dilatih
        fold_pred, fold_prob = get_predictions(trainer, fold_val_dataset)
        fold_true = np.array(fold_val_labels).astype(int)
        
        # Hitung akurasi untuk fold ini
        fold_accuracy = accuracy_score(fold_true, fold_pred)
        cv_scores.append(fold_accuracy)
        
        print(f"  Fold {fold} Accuracy: {fold_accuracy:.4f} ({fold_accuracy*100:.2f}%)")
    
    return cv_scores

# Jalankan cross-validation
cv_scores = manual_cross_validation(df, k_folds=5)

# Tampilkan hasil cross-validation
print(f"\n=== Hasil Cross-Validation ===")
print(f"Scores per fold: {[f'{score:.4f}' for score in cv_scores]}")
print(f"Mean CV Score: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
print(f"Min Score: {np.min(cv_scores):.4f}")
print(f"Max Score: {np.max(cv_scores):.4f}")

# Interpretasi hasil
cv_std = np.std(cv_scores)
if cv_std < 0.02:
    print(f"\n✅ Model menunjukkan stabilitas BAIK (std: {cv_std:.4f} < 0.02)")
elif cv_std < 0.05:
    print(f"\n⚠️  Model menunjukkan stabilitas SEDANG (std: {cv_std:.4f})")
else:
    print(f"\n❌ Model menunjukkan stabilitas BURUK (std: {cv_std:.4f} > 0.05)")

# Analisis Overfitting: Perbandingan performa Train vs Validation
print("=== ANALISIS OVERFITTING ===")
print("\nPerbandingan Metrik Train vs Validation:")
print("-" * 50)

metrics_comparison = {
    'Metric': ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC'],
    'Training': [train_metrics['accuracy'], train_metrics['precision'], 
                train_metrics['recall'], train_metrics['f1_score'], train_metrics['auc_score']],
    'Validation': [val_metrics['accuracy'], val_metrics['precision'], 
                  val_metrics['recall'], val_metrics['f1_score'], val_metrics['auc_score']],
}

# Hitung selisih (gap) antara training dan validation
gaps = []
for i in range(len(metrics_comparison['Training'])):
    gap = metrics_comparison['Training'][i] - metrics_comparison['Validation'][i]
    gaps.append(gap)
    
metrics_comparison['Gap (Train-Val)'] = gaps

# Tampilkan dalam format tabel
comparison_df = pd.DataFrame(metrics_comparison)
print(comparison_df.round(4))

# Analisis overfitting berdasarkan gap
avg_gap = np.mean([abs(gap) for gap in gaps])
max_gap = max([abs(gap) for gap in gaps])

print(f"\n=== Interpretasi Overfitting ===")
print(f"Average Gap: {avg_gap:.4f}")
print(f"Maximum Gap: {max_gap:.4f}")

if max_gap < 0.05:
    overfitting_status = "TIDAK ADA OVERFITTING"
    color = "✅"
elif max_gap < 0.10:
    overfitting_status = "OVERFITTING RINGAN"
    color = "⚠️"
elif max_gap < 0.15:
    overfitting_status = "OVERFITTING SEDANG"
    color = "🔶"
else:
    overfitting_status = "OVERFITTING BERAT"
    color = "❌"

print(f"\n{color} Status: {overfitting_status}")

# Rekomendasi berdasarkan hasil
print(f"\n=== Rekomendasi ===")
if max_gap < 0.05:
    print("Model memiliki generalisasi yang baik. Tidak perlu tindakan khusus.")
elif max_gap < 0.10:
    print("Model sedikit overfit. Pertimbangkan:")
    print("- Menambah data validasi")
    print("- Menggunakan regularisasi yang lebih kuat")
else:
    print("Model mengalami overfitting. Tindakan yang disarankan:")
    print("- Mengurangi kompleksitas model")
    print("- Menambah dropout rate")
    print("- Menggunakan early stopping yang lebih agresif")
    print("- Menambah data training")
    print("- Menggunakan data augmentation")

# ROC Curve dan AUC Analysis
plt.figure(figsize=(15, 5))

# Plot 1: ROC Curve untuk Training Set
plt.subplot(1, 3, 1)
fpr_train, tpr_train, _ = roc_curve(train_true, train_prob)
auc_train = auc(fpr_train, tpr_train)

plt.plot(fpr_train, tpr_train, color='blue', lw=2, 
         label=f'Training ROC (AUC = {auc_train:.4f})')
plt.plot([0, 1], [0, 1], color='red', lw=1, linestyle='--', label='Random Classifier')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('ROC Curve - Training Set')
plt.legend(loc="lower right")
plt.grid(True, alpha=0.3)

# Plot 2: ROC Curve untuk Validation Set
plt.subplot(1, 3, 2)
fpr_val, tpr_val, _ = roc_curve(val_true, val_prob)
auc_val = auc(fpr_val, tpr_val)

plt.plot(fpr_val, tpr_val, color='green', lw=2, 
         label=f'Validation ROC (AUC = {auc_val:.4f})')
plt.plot([0, 1], [0, 1], color='red', lw=1, linestyle='--', label='Random Classifier')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('ROC Curve - Validation Set')
plt.legend(loc="lower right")
plt.grid(True, alpha=0.3)

# Plot 3: Perbandingan ROC Curves
plt.subplot(1, 3, 3)
plt.plot(fpr_train, tpr_train, color='blue', lw=2, 
         label=f'Training (AUC = {auc_train:.4f})')
plt.plot(fpr_val, tpr_val, color='green', lw=2, 
         label=f'Validation (AUC = {auc_val:.4f})')
plt.plot([0, 1], [0, 1], color='red', lw=1, linestyle='--', label='Random')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('ROC Curves Comparison')
plt.legend(loc="lower right")
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Interpretasi AUC Score
print("=== INTERPRETASI AUC SCORE ===")
print(f"Training AUC: {auc_train:.4f}")
print(f"Validation AUC: {auc_val:.4f}")
print(f"AUC Gap: {abs(auc_train - auc_val):.4f}")

def interpret_auc(auc_score, dataset_name):
    if auc_score >= 0.9:
        return f"🟢 {dataset_name}: EXCELLENT (AUC ≥ 0.9)"
    elif auc_score >= 0.8:
        return f"🔵 {dataset_name}: GOOD (0.8 ≤ AUC < 0.9)"
    elif auc_score >= 0.7:
        return f"🟡 {dataset_name}: FAIR (0.7 ≤ AUC < 0.8)"
    elif auc_score >= 0.6:
        return f"🟠 {dataset_name}: POOR (0.6 ≤ AUC < 0.7)"
    else:
        return f"🔴 {dataset_name}: FAIL (AUC < 0.6)"

print(f"\n{interpret_auc(auc_train, 'Training')}")
print(f"{interpret_auc(auc_val, 'Validation')}")

# Analisis diskriminasi model
if auc_val >= 0.8:
    print(f"\n✅ Model memiliki kemampuan diskriminasi yang BAIK (AUC > 0.8)")
elif auc_val >= 0.7:
    print(f"\n⚠️  Model memiliki kemampuan diskriminasi yang CUKUP (AUC > 0.7)")
else:
    print(f"\n❌ Model memiliki kemampuan diskriminasi yang BURUK (AUC < 0.7)")

# Confusion Matrix Analysis
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Confusion Matrix untuk Training Set
cm_train = confusion_matrix(train_true, train_pred)
sns.heatmap(cm_train, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['Non-Spam', 'Spam'], 
            yticklabels=['Non-Spam', 'Spam'],
            ax=axes[0])
axes[0].set_title('Confusion Matrix - Training Set')
axes[0].set_xlabel('Predicted Label')
axes[0].set_ylabel('True Label')

# Confusion Matrix untuk Validation Set
cm_val = confusion_matrix(val_true, val_pred)
sns.heatmap(cm_val, annot=True, fmt='d', cmap='Greens', 
            xticklabels=['Non-Spam', 'Spam'], 
            yticklabels=['Non-Spam', 'Spam'],
            ax=axes[1])
axes[1].set_title('Confusion Matrix - Validation Set')
axes[1].set_xlabel('Predicted Label')
axes[1].set_ylabel('True Label')

plt.tight_layout()
plt.show()

# Analisis detail Confusion Matrix
def analyze_confusion_matrix(cm, dataset_name):
    """Analisis detail confusion matrix"""
    tn, fp, fn, tp = cm.ravel()
    
    print(f"\n=== Analisis Confusion Matrix - {dataset_name} ===")
    print(f"True Negatives (TN):  {tn:4d} - Correctly predicted Non-Spam")
    print(f"False Positives (FP): {fp:4d} - Incorrectly predicted as Spam")
    print(f"False Negatives (FN): {fn:4d} - Incorrectly predicted as Non-Spam")
    print(f"True Positives (TP):  {tp:4d} - Correctly predicted Spam")
    
    # Hitung metrik dari confusion matrix
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    precision_spam = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall_spam = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    print(f"\nMetrik dari Confusion Matrix:")
    print(f"Accuracy:           {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"Precision (Spam):   {precision_spam:.4f} ({precision_spam*100:.2f}%)")
    print(f"Recall (Spam):      {recall_spam:.4f} ({recall_spam*100:.2f}%)")
    print(f"Specificity:        {specificity:.4f} ({specificity*100:.2f}%)")
    
    # Error Analysis
    total_errors = fp + fn
    total_samples = tp + tn + fp + fn
    error_rate = total_errors / total_samples
    
    print(f"\nError Analysis:")
    print(f"Total Errors:       {total_errors} / {total_samples} ({error_rate*100:.2f}%)")
    print(f"False Positive Rate: {fp/(fp+tn)*100:.2f}% (Non-spam classified as spam)")
    print(f"False Negative Rate: {fn/(fn+tp)*100:.2f}% (Spam classified as non-spam)")
    
    return {
        'tn': tn, 'fp': fp, 'fn': fn, 'tp': tp,
        'accuracy': accuracy, 'precision': precision_spam, 
        'recall': recall_spam, 'specificity': specificity
    }

# Analisis untuk training dan validation set
train_cm_analysis = analyze_confusion_matrix(cm_train, "Training Set")
val_cm_analysis = analyze_confusion_matrix(cm_val, "Validation Set")

# Comprehensive Testing Summary
print("\n" + "="*80)
print("                    COMPREHENSIVE MODEL TESTING SUMMARY")
print("="*80)

# 1. Model Accuracy Summary
print("\n1. MODEL ACCURACY TESTING:")
print("-" * 40)
print(f"Training Accuracy:    {train_metrics['accuracy']:.4f} ({train_metrics['accuracy']*100:.2f}%)")
print(f"Validation Accuracy:  {val_metrics['accuracy']:.4f} ({val_metrics['accuracy']*100:.2f}%)")
print(f"Training F1-Score:    {train_metrics['f1_score']:.4f}")
print(f"Validation F1-Score:  {val_metrics['f1_score']:.4f}")

# 2. Cross-Validation Summary
print("\n2. CROSS-VALIDATION TESTING:")
print("-" * 40)
print(f"Mean CV Score:        {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
print(f"CV Stability:         {cv_std:.4f} ({'GOOD' if cv_std < 0.02 else 'MODERATE' if cv_std < 0.05 else 'POOR'})")

# 3. Overfitting Analysis Summary
print("\n3. OVERFITTING TESTING:")
print("-" * 40)
print(f"Average Gap:          {avg_gap:.4f}")
print(f"Maximum Gap:          {max_gap:.4f}")
print(f"Overfitting Status:   {overfitting_status}")

# 4. ROC & AUC Summary
print("\n4. ROC CURVE & AUC TESTING:")
print("-" * 40)
print(f"Training AUC:         {auc_train:.4f} ({'EXCELLENT' if auc_train >= 0.9 else 'GOOD' if auc_train >= 0.8 else 'FAIR'})")
print(f"Validation AUC:       {auc_val:.4f} ({'EXCELLENT' if auc_val >= 0.9 else 'GOOD' if auc_val >= 0.8 else 'FAIR'})")
print(f"AUC Gap:              {abs(auc_train - auc_val):.4f}")

# 5. Confusion Matrix Summary
print("\n5. CONFUSION MATRIX ANALYSIS:")
print("-" * 40)
print("Validation Set Results:")
print(f"  True Positives:     {val_cm_analysis['tp']} (Correctly identified spam)")
print(f"  True Negatives:     {val_cm_analysis['tn']} (Correctly identified non-spam)")
print(f"  False Positives:    {val_cm_analysis['fp']} (Non-spam classified as spam)")
print(f"  False Negatives:    {val_cm_analysis['fn']} (Spam classified as non-spam)")

# Overall Assessment
print("\n" + "="*80)
print("                        OVERALL MODEL ASSESSMENT")
print("="*80)

# Scoring system
score = 0
max_score = 5

# Accuracy score (20%)
if val_metrics['accuracy'] >= 0.9:
    score += 1
    acc_grade = "EXCELLENT"
elif val_metrics['accuracy'] >= 0.8:
    score += 0.8
    acc_grade = "GOOD"
elif val_metrics['accuracy'] >= 0.7:
    score += 0.6
    acc_grade = "FAIR"
else:
    acc_grade = "POOR"

# CV Stability score (20%)
if cv_std < 0.02:
    score += 1
    cv_grade = "EXCELLENT"
elif cv_std < 0.05:
    score += 0.7
    cv_grade = "GOOD"
else:
    cv_grade = "POOR"

# Overfitting score (20%)
if max_gap < 0.05:
    score += 1
    of_grade = "NO OVERFITTING"
elif max_gap < 0.10:
    score += 0.7
    of_grade = "SLIGHT OVERFITTING"
else:
    of_grade = "SIGNIFICANT OVERFITTING"

# AUC score (20%)
if auc_val >= 0.9:
    score += 1
    auc_grade = "EXCELLENT"
elif auc_val >= 0.8:
    score += 0.8
    auc_grade = "GOOD"
elif auc_val >= 0.7:
    score += 0.6
    auc_grade = "FAIR"
else:
    auc_grade = "POOR"

# F1-Score (20%)
if val_metrics['f1_score'] >= 0.9:
    score += 1
    f1_grade = "EXCELLENT"
elif val_metrics['f1_score'] >= 0.8:
    score += 0.8
    f1_grade = "GOOD"
elif val_metrics['f1_score'] >= 0.7:
    score += 0.6
    f1_grade = "FAIR"
else:
    f1_grade = "POOR"

final_score = (score / max_score) * 100

print(f"Accuracy Grade:       {acc_grade}")
print(f"CV Stability Grade:   {cv_grade}")
print(f"Overfitting Grade:    {of_grade}")
print(f"AUC Grade:            {auc_grade}")
print(f"F1-Score Grade:       {f1_grade}")
print(f"\nFINAL MODEL SCORE:    {final_score:.1f}/100")

if final_score >= 90:
    final_grade = "🏆 EXCELLENT - Model ready for production"
elif final_score >= 80:
    final_grade = "🥇 GOOD - Model performs well with minor improvements needed"
elif final_score >= 70:
    final_grade = "🥈 FAIR - Model needs significant improvements"
else:
    final_grade = "🥉 POOR - Model requires major revisions"

print(f"FINAL GRADE:          {final_grade}")

print("\n" + "="*80)
print("                    TESTING COMPLETED SUCCESSFULLY")
print("="*80)

# Final Recommendations and Conclusions
print("\n" + "="*80)
print("                    REKOMENDASI DAN KESIMPULAN")
print("="*80)

print("\n📊 RINGKASAN HASIL PENGUJIAN:")
print("-" * 50)
print(f"✓ Model Accuracy Testing: Akurasi validasi {val_metrics['accuracy']*100:.2f}%")
print(f"✓ Cross-Validation: Stabilitas {cv_std:.4f} ({'baik' if cv_std < 0.02 else 'sedang' if cv_std < 0.05 else 'buruk'})")
print(f"✓ Overfitting Testing: Gap maksimal {max_gap:.4f} ({overfitting_status.lower()})")
print(f"✓ ROC & AUC Testing: AUC {auc_val:.4f} ({'excellent' if auc_val >= 0.9 else 'good' if auc_val >= 0.8 else 'fair'})")
print(f"✓ Confusion Matrix: {val_cm_analysis['tp']} TP, {val_cm_analysis['tn']} TN, {val_cm_analysis['fp']} FP, {val_cm_analysis['fn']} FN")

print("\n🎯 REKOMENDASI BERDASARKAN HASIL:")
print("-" * 50)

if final_score >= 90:
    print("🟢 MODEL SIAP PRODUKSI:")
    print("   • Model menunjukkan performa excellent di semua aspek")
    print("   • Dapat diimplementasikan langsung ke production")
    print("   • Lakukan monitoring berkala untuk memastikan performa tetap stabil")
    print("   • Pertimbangkan A/B testing untuk validasi di real-world scenario")
    
elif final_score >= 80:
    print("🟡 MODEL PERLU PERBAIKAN MINOR:")
    print("   • Model memiliki performa yang baik namun masih bisa ditingkatkan")
    
    if val_metrics['accuracy'] < 0.9:
        print("   • Tingkatkan akurasi dengan fine-tuning hyperparameter")
    
    if cv_std >= 0.02:
        print("   • Perbaiki stabilitas dengan menambah data training")
    
    if max_gap >= 0.05:
        print("   • Kurangi overfitting dengan regularisasi yang lebih kuat")
    
    if auc_val < 0.9:
        print("   • Tingkatkan kemampuan diskriminasi model")
    
    print("   • Lakukan testing tambahan sebelum production deployment")
    
elif final_score >= 70:
    print("🟠 MODEL PERLU PERBAIKAN SIGNIFIKAN:")
    print("   • Model memerlukan improvement yang substansial")
    print("   • Pertimbangkan arsitektur model yang berbeda")
    print("   • Tambah data training yang lebih berkualitas")
    print("   • Lakukan feature engineering yang lebih mendalam")
    print("   • Eksperimen dengan teknik ensemble methods")
    
else:
    print("🔴 MODEL PERLU REVISI MAJOR:")
    print("   • Model tidak memenuhi standar minimum untuk production")
    print("   • Kembali ke tahap data preparation dan feature engineering")
    print("   • Pertimbangkan menggunakan model yang berbeda")
    print("   • Evaluasi kualitas dan kuantitas data training")
    print("   • Konsultasi dengan domain expert untuk insight tambahan")

print("\n🔧 LANGKAH SELANJUTNYA:")
print("-" * 50)
print("1. Dokumentasikan semua hasil testing ini")
print("2. Buat model versioning untuk tracking perubahan")
print("3. Setup monitoring system untuk production deployment")
print("4. Siapkan fallback mechanism jika model performance menurun")
print("5. Rencanakan schedule untuk model retraining berkala")

print("\n📈 METRIK MONITORING YANG DISARANKAN:")
print("-" * 50)
print("• Accuracy, Precision, Recall, F1-Score (harian)")
print("• AUC Score dan ROC Curve (mingguan)")
print("• Confusion Matrix analysis (mingguan)")
print("• Data drift detection (bulanan)")
print("• Model performance degradation alerts")

print("\n" + "="*80)
print(f"           COMPREHENSIVE TESTING COMPLETED - SCORE: {final_score:.1f}/100")
print("="*80)

# Save testing results to a summary file (optional)
testing_summary = {
    'model_accuracy': {
        'train_accuracy': train_metrics['accuracy'],
        'val_accuracy': val_metrics['accuracy'],
        'train_f1': train_metrics['f1_score'],
        'val_f1': val_metrics['f1_score']
    },
    'cross_validation': {
        'cv_scores': cv_scores,
        'cv_mean': np.mean(cv_scores),
        'cv_std': cv_std
    },
    'overfitting': {
        'avg_gap': avg_gap,
        'max_gap': max_gap,
        'status': overfitting_status
    },
    'roc_auc': {
        'train_auc': auc_train,
        'val_auc': auc_val,
        'auc_gap': abs(auc_train - auc_val)
    },
    'confusion_matrix': {
        'tp': int(val_cm_analysis['tp']),
        'tn': int(val_cm_analysis['tn']),
        'fp': int(val_cm_analysis['fp']),
        'fn': int(val_cm_analysis['fn'])
    },
    'final_score': final_score,
    'final_grade': final_grade
}

print(f"\n💾 Testing summary saved to variable 'testing_summary'")
print(f"📋 Use this data for reporting and documentation purposes")

# Final Comprehensive Visualization
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Comprehensive Model Testing Results', fontsize=16, fontweight='bold')

# 1. Metrics Comparison Bar Chart
metrics_names = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC']
train_values = [train_metrics['accuracy'], train_metrics['precision'], 
               train_metrics['recall'], train_metrics['f1_score'], train_metrics['auc_score']]
val_values = [val_metrics['accuracy'], val_metrics['precision'], 
             val_metrics['recall'], val_metrics['f1_score'], val_metrics['auc_score']]

x = np.arange(len(metrics_names))
width = 0.35

axes[0,0].bar(x - width/2, train_values, width, label='Training', color='skyblue', alpha=0.8)
axes[0,0].bar(x + width/2, val_values, width, label='Validation', color='lightcoral', alpha=0.8)
axes[0,0].set_xlabel('Metrics')
axes[0,0].set_ylabel('Score')
axes[0,0].set_title('Model Performance Metrics Comparison')
axes[0,0].set_xticks(x)
axes[0,0].set_xticklabels(metrics_names, rotation=45)
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)
axes[0,0].set_ylim(0, 1.1)

# Add value labels on bars
for i, (train_val, val_val) in enumerate(zip(train_values, val_values)):
    axes[0,0].text(i - width/2, train_val + 0.01, f'{train_val:.3f}', 
                  ha='center', va='bottom', fontsize=8)
    axes[0,0].text(i + width/2, val_val + 0.01, f'{val_val:.3f}', 
                  ha='center', va='bottom', fontsize=8)

# 2. Cross-Validation Scores
fold_numbers = [f'Fold {i+1}' for i in range(len(cv_scores))]
axes[0,1].plot(fold_numbers, cv_scores, 'o-', color='green', linewidth=2, markersize=8)
axes[0,1].axhline(y=np.mean(cv_scores), color='red', linestyle='--', 
                 label=f'Mean: {np.mean(cv_scores):.4f}')
axes[0,1].fill_between(range(len(cv_scores)), 
                      np.mean(cv_scores) - np.std(cv_scores),
                      np.mean(cv_scores) + np.std(cv_scores),
                      alpha=0.2, color='red')
axes[0,1].set_xlabel('Cross-Validation Folds')
axes[0,1].set_ylabel('Accuracy Score')
axes[0,1].set_title('Cross-Validation Stability')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)
axes[0,1].set_ylim(min(cv_scores) - 0.01, max(cv_scores) + 0.01)

# 3. Overfitting Analysis
gap_metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC']
gap_values = [abs(train_metrics['accuracy'] - val_metrics['accuracy']),
             abs(train_metrics['precision'] - val_metrics['precision']),
             abs(train_metrics['recall'] - val_metrics['recall']),
             abs(train_metrics['f1_score'] - val_metrics['f1_score']),
             abs(train_metrics['auc_score'] - val_metrics['auc_score'])]

colors = ['red' if gap > 0.05 else 'orange' if gap > 0.02 else 'green' for gap in gap_values]
bars = axes[1,0].bar(gap_metrics, gap_values, color=colors, alpha=0.7)
axes[1,0].axhline(y=0.05, color='red', linestyle='--', alpha=0.7, label='High Overfitting (>0.05)')
axes[1,0].axhline(y=0.02, color='orange', linestyle='--', alpha=0.7, label='Moderate Overfitting (>0.02)')
axes[1,0].set_xlabel('Metrics')
axes[1,0].set_ylabel('Gap (Train - Validation)')
axes[1,0].set_title('Overfitting Analysis (Train-Val Gap)')
axes[1,0].legend()
axes[1,0].grid(True, alpha=0.3)
plt.setp(axes[1,0].xaxis.get_majorticklabels(), rotation=45)

# Add value labels on bars
for bar, gap in zip(bars, gap_values):
    height = bar.get_height()
    axes[1,0].text(bar.get_x() + bar.get_width()/2., height + 0.001,
                  f'{gap:.4f}', ha='center', va='bottom', fontsize=8)

# 4. Model Score Breakdown
score_components = ['Accuracy', 'CV Stability', 'No Overfitting', 'AUC Score', 'F1-Score']
component_scores = []

# Calculate individual component scores (out of 20 each)
acc_score = 20 if val_metrics['accuracy'] >= 0.9 else 16 if val_metrics['accuracy'] >= 0.8 else 12 if val_metrics['accuracy'] >= 0.7 else 8
cv_score = 20 if cv_std < 0.02 else 14 if cv_std < 0.05 else 8
of_score = 20 if max_gap < 0.05 else 14 if max_gap < 0.10 else 8
auc_score = 20 if auc_val >= 0.9 else 16 if auc_val >= 0.8 else 12 if auc_val >= 0.7 else 8
f1_score = 20 if val_metrics['f1_score'] >= 0.9 else 16 if val_metrics['f1_score'] >= 0.8 else 12 if val_metrics['f1_score'] >= 0.7 else 8

component_scores = [acc_score, cv_score, of_score, auc_score, f1_score]
colors = ['green' if score >= 18 else 'orange' if score >= 14 else 'red' for score in component_scores]

bars = axes[1,1].bar(score_components, component_scores, color=colors, alpha=0.7)
axes[1,1].axhline(y=20, color='green', linestyle='--', alpha=0.7, label='Excellent (≥18/20)')
axes[1,1].axhline(y=14, color='orange', linestyle='--', alpha=0.7, label='Good (≥14/20)')
axes[1,1].set_xlabel('Testing Components')
axes[1,1].set_ylabel('Score (out of 20)')
axes[1,1].set_title(f'Model Score Breakdown (Total: {final_score:.1f}/100)')
axes[1,1].legend()
axes[1,1].grid(True, alpha=0.3)
axes[1,1].set_ylim(0, 22)
plt.setp(axes[1,1].xaxis.get_majorticklabels(), rotation=45)

# Add value labels on bars
for bar, score in zip(bars, component_scores):
    height = bar.get_height()
    axes[1,1].text(bar.get_x() + bar.get_width()/2., height + 0.3,
                  f'{score}/20', ha='center', va='bottom', fontsize=8, fontweight='bold')

plt.tight_layout()
plt.show()

# Print final summary
print("\n🎉 COMPREHENSIVE MODEL TESTING COMPLETED!")
print(f"📊 Final Model Score: {final_score:.1f}/100")
print(f"🏆 Grade: {final_grade}")
print("\n📈 All testing results have been visualized above.")
print("📋 Refer to the testing_summary variable for detailed results.")

!zip -r indobert-judul-classifier.zip indobert-judol-classifier

from google.colab import files
files.download('indobert-judul-classifier.zip')