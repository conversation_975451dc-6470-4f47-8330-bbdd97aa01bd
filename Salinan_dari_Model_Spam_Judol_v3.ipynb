#Install Libraries (Jika di Google Colab)
# !pip install transformers datasets scikit-learn wordcloud

import pandas as pd
import seaborn as sns
from sklearn.model_selection import train_test_split
from datasets import Dataset
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
import torch

#Load & Preprocess Data
#kolom 'komentar' dan 'label' (0=tidak judol, 1=judol)
df = pd.read_csv('Dataset.csv')
df

# Menampilkan 5 baris pertama untuk melihat sampel data
print("\nSampel Data:")
print(df.head())

# Melihat informasi ringkas (tipe data, nilai non-null)
print("\nInformasi Dataset:")
df.info()


# Informasi umum tentang dataset
print("=== Informasi Dataset ===")
print(f"Jumlah baris: {df.shape[0]}")
print(f"Jumlah kolom: {df.shape[1]}")
print(f"\nNama kolom: {list(df.columns)}")
print(f"\nTipe data:")
print(df.dtypes)
print(f"\nInformasi missing values:")
print(df.isnull().sum())

# Distribusi label dalam bentuk angka
print("=== Distribusi Label ===")
label_counts = df['Label'].value_counts()
print(label_counts)
print(f"\nPersentase:")
print(df['Label'].value_counts(normalize=True) * 100)

# Analisis panjang teks
print("=== Analisis Panjang Teks ===")

# Pastikan kolom Comment ada
if 'Comment' in df.columns:
    # Buat kolom sementara untuk analisis
    df['text_length'] = df['Comment'].str.len()
    df['word_count'] = df['Comment'].str.split().str.len()

    print(f"Statistik panjang karakter:")
    print(df['text_length'].describe())
    print(f"\nStatistik jumlah kata:")
    print(df['word_count'].describe())
else:
    print("❌ Error: Kolom 'Comment' tidak ditemukan!")
    print(f"Kolom yang tersedia: {list(df.columns)}")

# Perbandingan panjang teks berdasarkan label
print("=== Perbandingan Panjang Teks Berdasarkan Label ===")

# Pastikan kolom yang diperlukan ada
if 'text_length' in df.columns and 'word_count' in df.columns:
    print("\nPanjang karakter berdasarkan label:")
    print(df.groupby('Label')['text_length'].describe())
    print("\nJumlah kata berdasarkan label:")
    print(df.groupby('Label')['word_count'].describe())
else:
    print("❌ Error: Kolom 'text_length' atau 'word_count' tidak ditemukan!")
    print("Pastikan cell sebelumnya (Analisis Panjang Teks) sudah dijalankan.")
    print(f"Kolom yang tersedia: {list(df.columns)}")

print(df.columns)

# PENTING: Membersihkan kolom sementara yang dibuat untuk analisis
# Kolom ini hanya diperlukan untuk analisis data understanding
# dan akan dihapus sebelum preprocessing untuk menghindari konflik

print("=== Pembersihan Kolom Sementara ===")
print(f"Kolom sebelum pembersihan: {df.columns.tolist()}")

if 'text_length' in df.columns:
    df = df.drop(['text_length', 'word_count'], axis=1)
    print("✅ Kolom sementara berhasil dihapus: text_length, word_count")
else:
    print("ℹ️  Kolom sementara tidak ditemukan (sudah dihapus atau belum dibuat)")

print(f"Kolom setelah pembersihan: {df.columns.tolist()}")
print(f"\n⚠️  CATATAN: Jika Anda mendapat error 'KeyError: text_length',")
print(f"   pastikan untuk menjalankan cell ini setelah menjalankan")
print(f"   semua cell di bagian Data Understanding.")

#Pastikan semua nama kolom lower case agar konsisten
df.columns = df.columns.str.lower()
print("Nama kolom setelah normalisasi:", df.columns.tolist())

#Cek nama kolom label (asumsi: 'label' dalam huruf kecil)
if 'label' not in df.columns:
    print("Nama kolom label:", df.columns)  # tampilkan semua nama kolom agar bisa dicek
    raise ValueError("Kolom 'label' tidak ditemukan!")

# Mengecek jumlah data duplikat sebelum dihapus
print(f"Jumlah data duplikat sebelum dibersihkan: {df.duplicated().sum()}")

# Menghapus data duplikat
df.drop_duplicates(inplace=True)

print(f"Jumlah data duplikat setelah dibersihkan: {df.duplicated().sum()}")

#Buang baris dengan label kosong (NaN)
initial_count = len(df)
df = df.dropna(subset=['label'])
after_count = len(df)
print(f"Data sebelum: {initial_count}, setelah buang label kosong: {after_count}")

#Split data (stratify harus label tanpa NaN)
train_df, val_df = train_test_split(
    df,
    test_size=0.2,
    stratify=df['label'],
    random_state=42
)

print("=== Informasi Data Splitting ===")
print(f"Jumlah train data: {len(train_df)}")
print(f"Jumlah validasi data: {len(val_df)}")
print(f"\nDistribusi label di training set:")
print(train_df['label'].value_counts(normalize=True) * 100)
print(f"\nDistribusi label di validation set:")
print(val_df['label'].value_counts(normalize=True) * 100)

# Menampilkan contoh komentar spam (label = 1)
print("=== Contoh Komentar SPAM (Label = 1) ===")
spam_samples = df[df['label'] == 1.0]['comment'].head(10)
for i, comment in enumerate(spam_samples, 1):
    print(f"{i}. {comment}")
    print()

# Menampilkan contoh komentar non-spam (label = 0)
print("=== Contoh Komentar NON-SPAM (Label = 0) ===")
non_spam_samples = df[df['label'] == 0.0]['comment'].head(10)
for i, comment in enumerate(non_spam_samples, 1):
    print(f"{i}. {comment}")
    print()

import matplotlib.pyplot as plt
import seaborn as sns

# Set style untuk visualisasi
plt.style.use('default')
sns.set_palette("husl")

# Membuat subplot untuk visualisasi
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. Bar plot distribusi label
sns.countplot(data=df, x='label', ax=axes[0,0])
axes[0,0].set_title('Distribusi Label (Bar Plot)')
axes[0,0].set_xlabel('Label (0=Non-Spam, 1=Spam)')
axes[0,0].set_ylabel('Jumlah')

# 2. Pie chart distribusi label
label_counts = df['label'].value_counts()
axes[0,1].pie(label_counts.values, labels=['Spam', 'Non-Spam'], autopct='%1.1f%%', startangle=90)
axes[0,1].set_title('Distribusi Label (Pie Chart)')

# 3. Histogram panjang teks (jika kolom tersedia)
if 'text_length' in df.columns:
    axes[1,0].hist(df[df['Label']==0]['text_length'], alpha=0.7, label='Non-Spam', bins=30)
    axes[1,0].hist(df[df['Label']==1]['text_length'], alpha=0.7, label='Spam', bins=30)
    axes[1,0].set_title('Distribusi Panjang Teks')
    axes[1,0].set_xlabel('Panjang Karakter')
    axes[1,0].set_ylabel('Frekuensi')
    axes[1,0].legend()
else:
    axes[1,0].text(0.5, 0.5, 'Kolom text_length\ntidak tersedia\n\nJalankan cell analisis\npanjang teks terlebih dahulu',
                   ha='center', va='center', transform=axes[1,0].transAxes, fontsize=10)
    axes[1,0].set_title('Distribusi Panjang Teks (Data Tidak Tersedia)')

# 4. Box plot panjang teks berdasarkan label (jika kolom tersedia)
if 'text_length' in df.columns:
    sns.boxplot(data=df, x='label', y='text_length', ax=axes[1,1])
    axes[1,1].set_title('Box Plot Panjang Teks per Label')
    axes[1,1].set_xlabel('Label (0=Non-Spam, 1=Spam)')
    axes[1,1].set_ylabel('Panjang Karakter')
else:
    axes[1,1].text(0.5, 0.5, 'Kolom text_length\ntidak tersedia\n\nJalankan cell analisis\npanjang teks terlebih dahulu',
                   ha='center', va='center', transform=axes[1,1].transAxes, fontsize=10)
    axes[1,1].set_title('Box Plot Panjang Teks (Data Tidak Tersedia)')

plt.tight_layout()
plt.show()

from collections import Counter
import re

# Fungsi untuk membersihkan teks
def clean_text(text):
    # Mengubah ke lowercase dan menghapus karakter khusus
    text = re.sub(r'[^a-zA-Z\s]', '', text.lower())
    return text

# Menganalisis kata-kata paling umum untuk spam
spam_texts = df[df['label'] == 1.0]['comment'].apply(clean_text)
spam_words = ' '.join(spam_texts).split()
spam_word_freq = Counter(spam_words)

print("=== 20 Kata Paling Umum dalam SPAM ===")
for word, freq in spam_word_freq.most_common(20):
    print(f"{word}: {freq}")

# Menganalisis kata-kata paling umum untuk non-spam
non_spam_texts = df[df['label'] == 0.0]['comment'].apply(clean_text)
non_spam_words = ' '.join(non_spam_texts).split()
non_spam_word_freq = Counter(non_spam_words)

print("=== 20 Kata Paling Umum dalam NON-SPAM ===")
for word, freq in non_spam_word_freq.most_common(20):
    print(f"{word}: {freq}")

# Import library untuk word cloud
from wordcloud import WordCloud
import matplotlib.pyplot as plt

# Fungsi untuk membuat word cloud
def create_wordcloud(text_data, title, max_words=100):
    """
    Membuat word cloud dari data teks

    Parameters:
    text_data: string - teks yang akan dibuat word cloud
    title: string - judul untuk word cloud
    max_words: int - maksimal kata yang ditampilkan
    """
    # Konfigurasi word cloud
    wordcloud = WordCloud(
        width=800,
        height=400,
        background_color='white',
        max_words=max_words,
        colormap='viridis',
        relative_scaling=0.5,
        random_state=42
    ).generate(text_data)

    return wordcloud

# Persiapkan data teks untuk word cloud
print("=== Mempersiapkan Data untuk Word Cloud ===")

# Gabungkan semua teks spam yang sudah dibersihkan
spam_text_combined = ' '.join(spam_texts.dropna())
print(f"Total karakter teks spam: {len(spam_text_combined)}")

# Gabungkan semua teks non-spam yang sudah dibersihkan
non_spam_text_combined = ' '.join(non_spam_texts.dropna())
print(f"Total karakter teks non-spam: {len(non_spam_text_combined)}")

# Buat word cloud untuk spam dan non-spam
print("\n=== Membuat Word Cloud ===")

# Setup figure dengan 2 subplot
fig, axes = plt.subplots(1, 2, figsize=(16, 8))

# Word cloud untuk SPAM
if len(spam_text_combined.strip()) > 0:
    spam_wordcloud = create_wordcloud(spam_text_combined, 'SPAM Comments', max_words=100)
    axes[0].imshow(spam_wordcloud, interpolation='bilinear')
    axes[0].set_title('Word Cloud - SPAM Comments', fontsize=16, fontweight='bold', color='red')
    axes[0].axis('off')
else:
    axes[0].text(0.5, 0.5, 'Tidak ada data SPAM\nyang cukup untuk\nmembuat word cloud',
                ha='center', va='center', transform=axes[0].transAxes, fontsize=12)
    axes[0].set_title('Word Cloud - SPAM Comments (No Data)', fontsize=16)
    axes[0].axis('off')

# Word cloud untuk NON-SPAM
if len(non_spam_text_combined.strip()) > 0:
    non_spam_wordcloud = create_wordcloud(non_spam_text_combined, 'NON-SPAM Comments', max_words=100)
    axes[1].imshow(non_spam_wordcloud, interpolation='bilinear')
    axes[1].set_title('Word Cloud - NON-SPAM Comments', fontsize=16, fontweight='bold', color='green')
    axes[1].axis('off')
else:
    axes[1].text(0.5, 0.5, 'Tidak ada data NON-SPAM\nyang cukup untuk\nmembuat word cloud',
                ha='center', va='center', transform=axes[1].transAxes, fontsize=12)
    axes[1].set_title('Word Cloud - NON-SPAM Comments (No Data)', fontsize=16)
    axes[1].axis('off')

plt.tight_layout()
plt.show()

# Analisis tambahan dari word cloud
print("=== Analisis Word Cloud ===")

# Bandingkan kata-kata paling umum antara spam dan non-spam
print("\n1. PERBANDINGAN KATA PALING UMUM:")
print("\nTop 10 kata SPAM:")
for i, (word, freq) in enumerate(spam_word_freq.most_common(10), 1):
    print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

print("\nTop 10 kata NON-SPAM:")
for i, (word, freq) in enumerate(non_spam_word_freq.most_common(10), 1):
    print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

# Cari kata-kata yang unik untuk masing-masing kategori
spam_words_set = set(spam_word_freq.keys())
non_spam_words_set = set(non_spam_word_freq.keys())

# Kata yang hanya ada di spam
spam_only_words = spam_words_set - non_spam_words_set
# Kata yang hanya ada di non-spam
non_spam_only_words = non_spam_words_set - spam_words_set
# Kata yang ada di keduanya
common_words = spam_words_set & non_spam_words_set

print(f"\n2. DISTRIBUSI KATA:")
print(f"   - Kata unik di SPAM: {len(spam_only_words)}")
print(f"   - Kata unik di NON-SPAM: {len(non_spam_only_words)}")
print(f"   - Kata yang ada di keduanya: {len(common_words)}")
print(f"   - Total kata unik: {len(spam_words_set | non_spam_words_set)}")

# Tampilkan beberapa kata unik untuk masing-masing kategori
if len(spam_only_words) > 0:
    spam_only_sorted = sorted([(word, spam_word_freq[word]) for word in spam_only_words],
                             key=lambda x: x[1], reverse=True)
    print(f"\n3. KATA UNIK SPAM (Top 10):")
    for i, (word, freq) in enumerate(spam_only_sorted[:10], 1):
        print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

if len(non_spam_only_words) > 0:
    non_spam_only_sorted = sorted([(word, non_spam_word_freq[word]) for word in non_spam_only_words],
                                 key=lambda x: x[1], reverse=True)
    print(f"\n4. KATA UNIK NON-SPAM (Top 10):")
    for i, (word, freq) in enumerate(non_spam_only_sorted[:10], 1):
        print(f"   {i:2d}. {word:<15} ({freq:3d}x)")

# Analisis perbedaan frekuensi kata yang sama
if len(common_words) > 0:
    print(f"\n5. PERBEDAAN FREKUENSI KATA UMUM:")
    word_diff = []
    for word in common_words:
        spam_freq = spam_word_freq[word]
        non_spam_freq = non_spam_word_freq[word]
        diff_ratio = spam_freq / non_spam_freq if non_spam_freq > 0 else float('inf')
        word_diff.append((word, spam_freq, non_spam_freq, diff_ratio))

    # Urutkan berdasarkan rasio perbedaan
    word_diff.sort(key=lambda x: x[3], reverse=True)

    print("   Kata yang lebih sering muncul di SPAM:")
    for i, (word, spam_freq, non_spam_freq, ratio) in enumerate(word_diff[:5], 1):
        if ratio > 1:
            print(f"   {i}. {word:<15} - SPAM: {spam_freq:3d}x, NON-SPAM: {non_spam_freq:3d}x (rasio: {ratio:.2f})")

    print("\n   Kata yang lebih sering muncul di NON-SPAM:")
    word_diff.sort(key=lambda x: x[3])  # Urutkan ascending untuk yang lebih sering di non-spam
    for i, (word, spam_freq, non_spam_freq, ratio) in enumerate(word_diff[:5], 1):
        if ratio < 1:
            print(f"   {i}. {word:<15} - SPAM: {spam_freq:3d}x, NON-SPAM: {non_spam_freq:3d}x (rasio: {ratio:.2f})")

print("\n=== INSIGHT DARI WORD CLOUD ===")
print("✅ Word cloud membantu mengidentifikasi:")
print("   1. Kata-kata yang paling dominan dalam setiap kategori")
print("   2. Perbedaan visual antara pola kata spam vs non-spam")
print("   3. Kata-kata unik yang bisa menjadi indikator spam")
print("   4. Distribusi dan frekuensi kata secara visual")
print("\n💡 Informasi ini berguna untuk:")
print("   - Feature engineering (pemilihan kata kunci)")
print("   - Preprocessing (stopwords removal)")
print("   - Model training (understanding data patterns)")

# Word cloud untuk keseluruhan dataset
print("=== Word Cloud Keseluruhan Dataset ===")

# Gabungkan semua teks (spam + non-spam)
all_texts = df['comment'].apply(clean_text)
all_text_combined = ' '.join(all_texts.dropna())

print(f"Total karakter dalam dataset: {len(all_text_combined)}")
print(f"Total kata unik dalam dataset: {len(set(all_text_combined.split()))}")

# Buat word cloud untuk keseluruhan dataset
if len(all_text_combined.strip()) > 0:
    plt.figure(figsize=(12, 8))

    overall_wordcloud = create_wordcloud(all_text_combined, 'Overall Dataset', max_words=150)
    plt.imshow(overall_wordcloud, interpolation='bilinear')
    plt.title('Word Cloud - Keseluruhan Dataset (Spam + Non-Spam)',
              fontsize=18, fontweight='bold', pad=20)
    plt.axis('off')
    plt.tight_layout()
    plt.show()

    print("✅ Word cloud keseluruhan dataset berhasil dibuat!")

    # Statistik tambahan
    all_word_freq = Counter(all_text_combined.split())
    print(f"\n📊 STATISTIK KESELURUHAN:")
    print(f"   - Total kata: {sum(all_word_freq.values())}")
    print(f"   - Kata unik: {len(all_word_freq)}")
    print(f"   - Rata-rata frekuensi per kata: {sum(all_word_freq.values())/len(all_word_freq):.2f}")

    print(f"\n🔝 TOP 15 KATA PALING UMUM (Keseluruhan):")
    for i, (word, freq) in enumerate(all_word_freq.most_common(15), 1):
        percentage = (freq / sum(all_word_freq.values())) * 100
        print(f"   {i:2d}. {word:<15} ({freq:4d}x - {percentage:.2f}%)")

else:
    print("❌ Tidak ada data yang cukup untuk membuat word cloud keseluruhan")

# Mengecek duplikasi data
print("=== Analisis Duplikasi ===")
print(f"Jumlah data duplikat: {df.duplicated().sum()}")
print(f"Jumlah komentar unik: {df['comment'].nunique()}")
print(f"Total data: {len(df)}")

# Mengecek data kosong atau sangat pendek
print("\n=== Analisis Data Kosong/Pendek ===")
empty_comments = df[df['comment'].str.len() < 5]
print(f"Jumlah komentar dengan panjang < 5 karakter: {len(empty_comments)}")

if len(empty_comments) > 0:
    print("Contoh komentar pendek:")
    for comment in empty_comments['Comment'].head():
        print(f"'{comment}'")

# Ringkasan temuan dari data understanding
print("=== RINGKASAN DATA UNDERSTANDING ===")
print(f"\n1. INFORMASI DATASET:")
print(f"   - Total data: {len(df)} komentar")
print(f"   - Jumlah fitur: {df.shape[1]}")
print(f"   - Missing values: {df.isnull().sum().sum()}")

print(f"\n2. DISTRIBUSI LABEL:")
label_dist = df['label'].value_counts(normalize=True) * 100
print(f"   - Spam (1): {label_dist[1.0]:.1f}%")
print(f"   - Non-Spam (0): {label_dist[0.0]:.1f}%")
print(f"   - Dataset relatif seimbang: {'Ya' if abs(label_dist[1.0] - label_dist[0.0]) < 10 else 'Tidak'}")

print(f"\n3. KARAKTERISTIK TEKS:")
if 'text_length' in df.columns and 'word_count' in df.columns:
    print(f"   - Rata-rata panjang karakter: {df['text_length'].mean():.1f}")
    print(f"   - Rata-rata jumlah kata: {df['word_count'].mean():.1f}")
    print(f"   - Panjang teks spam vs non-spam: {'Berbeda signifikan' if abs(df[df['Label']==1]['text_length'].mean() - df[df['Label']==0]['text_length'].mean()) > 10 else 'Relatif sama'}")
else:
    print(f"   - Analisis karakteristik teks belum dilakukan")
    print(f"   - Jalankan cell 'Analisis Panjang Teks' terlebih dahulu")

print(f"\n4. KUALITAS DATA:")
print(f"   - Data duplikat: {df.duplicated().sum()}")
print(f"   - Komentar sangat pendek (<5 karakter): {len(df[df['comment'].str.len() < 5])}")
print(f"   - Kualitas data: {'Baik' if df.duplicated().sum() < len(df)*0.05 and len(df[df['comment'].str.len() < 5]) < len(df)*0.01 else 'Perlu pembersihan'}")

print(f"\n5. REKOMENDASI PREPROCESSING:")
print(f"   - Normalisasi teks (lowercase, remove special chars)")
print(f"   - Tokenisasi dan stemming/lemmatization")
print(f"   - Penanganan kata-kata umum (stopwords)")
print(f"   - Feature engineering (TF-IDF, word embeddings)")
if df.duplicated().sum() > 0:
    print(f"   - Penghapusan data duplikat")
if len(df[df['comment'].str.len() < 5]) > 0:
    print(f"   - Penanganan komentar sangat pendek")

df['label'].value_counts().plot(kind="pie",autopct="%.1f%%")
plt.title("Label")
plt.show()

#Load Tokenizer IndoBERT
print("=== Setup Tokenizer IndoBERT ===")
tokenizer = BertTokenizer.from_pretrained('indobenchmark/indobert-base-p1')
print("✅ IndoBERT tokenizer berhasil dimuat!")
print(f"   - Vocabulary size: {tokenizer.vocab_size}")
print(f"   - Model max length: {tokenizer.model_max_length}")

#Load Model IndoBERT for Classification
model = BertForSequenceClassification.from_pretrained(
    'indobenchmark/indobert-base-p1',
    num_labels=2  # binary classification
)

#Training Arguments & Metric
from transformers import TrainingArguments
training_args = TrainingArguments(
    output_dir='./results',
    eval_strategy='epoch',
    save_strategy='epoch',
    learning_rate=2e-5,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=32,
    num_train_epochs=3,
    weight_decay=0.01,
    logging_dir='./logs',
    logging_steps=20,
    save_total_limit=1,
    load_best_model_at_end=True,
    metric_for_best_model='accuracy'
)


from sklearn.metrics import accuracy_score, f1_score

def compute_metrics(eval_pred):
    logits, labels = eval_pred
    preds = logits.argmax(axis=-1)
    acc = accuracy_score(labels, preds)
    f1 = f1_score(labels, preds)
    return {'accuracy': acc, 'f1': f1}

# Setup Early Stopping untuk mencegah overfitting
from transformers import EarlyStoppingCallback

# Early stopping callback
early_stopping = EarlyStoppingCallback(
    early_stopping_patience=3,  # Stop jika tidak ada improvement dalam 3 evaluasi
    early_stopping_threshold=0.001  # Minimum improvement threshold
)

print("Early stopping callback telah disiapkan")
print("- Patience: 3 evaluasi")
print("- Threshold: 0.001")

# Custom callback untuk monitoring overfitting
from transformers import TrainerCallback
import matplotlib.pyplot as plt

class OverfittingMonitor(TrainerCallback):
    def __init__(self):
        self.train_losses = []
        self.eval_losses = []
        self.eval_accuracies = []
        self.steps = []

    def on_log(self, args, state, control, model=None, logs=None, **kwargs):
        if 'train_loss' in logs:
            self.train_losses.append(logs['train_loss'])
            self.steps.append(state.global_step)

    def on_evaluate(self, args, state, control, model=None, logs=None, **kwargs):
        if logs:
            if 'eval_loss' in logs:
                self.eval_losses.append(logs['eval_loss'])
            if 'eval_accuracy' in logs:
                self.eval_accuracies.append(logs['eval_accuracy'])

            # Cek overfitting
            if len(self.eval_losses) >= 2:
                current_eval_loss = self.eval_losses[-1]
                previous_eval_loss = self.eval_losses[-2]

                if current_eval_loss > previous_eval_loss:
                    print(f"⚠️  WARNING: Validation loss meningkat dari {previous_eval_loss:.4f} ke {current_eval_loss:.4f}")
                    print("   Kemungkinan mulai overfitting!")
                else:
                    print(f"✅ Validation loss turun: {previous_eval_loss:.4f} → {current_eval_loss:.4f}")

    def plot_training_progress(self):
        if len(self.eval_losses) > 0:
            plt.figure(figsize=(12, 4))

            # Plot losses
            plt.subplot(1, 2, 1)
            if len(self.train_losses) > 0:
                plt.plot(self.steps, self.train_losses, label='Training Loss', color='blue')
            if len(self.eval_losses) > 0:
                eval_steps = self.steps[::len(self.steps)//len(self.eval_losses)][:len(self.eval_losses)]
                plt.plot(eval_steps, self.eval_losses, label='Validation Loss', color='red', marker='o')
            plt.xlabel('Steps')
            plt.ylabel('Loss')
            plt.title('Training vs Validation Loss')
            plt.legend()
            plt.grid(True)

            # Plot accuracy
            plt.subplot(1, 2, 2)
            if len(self.eval_accuracies) > 0:
                eval_steps = self.steps[::len(self.steps)//len(self.eval_accuracies)][:len(self.eval_accuracies)]
                plt.plot(eval_steps, self.eval_accuracies, label='Validation Accuracy', color='green', marker='s')
            plt.xlabel('Steps')
            plt.ylabel('Accuracy')
            plt.title('Validation Accuracy')
            plt.legend()
            plt.grid(True)

            plt.tight_layout()
            plt.show()

# Inisialisasi monitor
overfitting_monitor = OverfittingMonitor()
print("Overfitting monitor telah disiapkan")

# Ensure labels are integers
df['label'] = df['label'].astype(int)


# Recreate train/val split with proper labels
train_df, val_df = train_test_split(df, test_size=0.2, stratify=df['label'], random_state=42)

# Rename label column to 'labels' for HuggingFace compatibility
train_df = train_df.rename(columns={'label': 'labels'})
val_df = val_df.rename(columns={'label': 'labels'})

# Create datasets
train_dataset = Dataset.from_pandas(train_df)
val_dataset = Dataset.from_pandas(val_df)

# Apply tokenization
def preprocess_func(examples):
    return tokenizer(
        examples['comment'],
        padding='max_length',
        truncation=True,
        max_length=128
    )

train_dataset = train_dataset.map(preprocess_func, batched=True)
val_dataset = val_dataset.map(preprocess_func, batched=True)

# Set format for PyTorch - Fix NumPy compatibility issue
import torch

# Remove unnecessary columns and set format
train_dataset = train_dataset.remove_columns(['__index_level_0__'] if '__index_level_0__' in train_dataset.column_names else [])
val_dataset = val_dataset.remove_columns(['__index_level_0__'] if '__index_level_0__' in val_dataset.column_names else [])

# Convert labels to proper format
def convert_labels(example):
    example['labels'] = int(example['labels'])
    return example

train_dataset = train_dataset.map(convert_labels)
val_dataset = val_dataset.map(convert_labels)

# Alternative approach - create custom data collator to avoid NumPy issues
from transformers import DataCollatorWithPadding

# Don't set format, let the data collator handle it
data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

print(f"Train dataset size: {len(train_dataset)}")
print(f"Validation dataset size: {len(val_dataset)}")
print(f"Train dataset columns: {train_dataset.column_names}")
print(f"Sample from train dataset: {train_dataset[0]}")

# Create Trainer with Fixed Datasets
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    compute_metrics=compute_metrics,
    callbacks=[early_stopping, overfitting_monitor]
)

# Fix for NumPy compatibility issue
import numpy as np
import warnings

# Suppress the specific NumPy warning
warnings.filterwarnings('ignore', message='Unable to avoid copy while creating an array')

# Alternative: Downgrade numpy if needed (uncomment if the above doesn't work)
# !pip install numpy==1.24.3

print(f"NumPy version: {np.__version__}")
print("NumPy compatibility fix applied")

#Training
trainer.train()

# Evaluation with Confusion Matrix

from sklearn.metrics import confusion_matrix, classification_report, ConfusionMatrixDisplay
import numpy as np
import matplotlib.pyplot as plt

print("=== Evaluasi Model dengan Confusion Matrix ===")

# Make predictions on the validation dataset
predictions = trainer.predict(val_dataset)
logits = predictions.predictions
labels = predictions.label_ids
preds = np.argmax(logits, axis=-1)

# Calculate confusion matrix
cm = confusion_matrix(labels, preds)

# Display confusion matrix
disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=['Non-Spam (0)', 'Spam (1)'])
disp.plot(cmap=plt.cm.Blues)
plt.title('Confusion Matrix')
plt.show()

# Print classification report
print("\n=== Classification Report ===")
print(classification_report(labels, preds, target_names=['Non-Spam (0)', 'Spam (1)']))

print("\n=== Summary of Evaluation ===")
print(f"Accuracy: {accuracy_score(labels, preds):.4f}")
print(f"F1 Score: {f1_score(labels, preds):.4f}")

# Plot training progress from the monitor
print("\n=== Training Progress Plots ===")
overfitting_monitor.plot_training_progress()

# 8. Save Trained Model
trainer.save_model('./indobert-judol-classifier')
tokenizer.save_pretrained('./indobert-judol-classifier')

!zip -r indobert-judul-classifier.zip indobert-judol-classifier

from google.colab import files
files.download('indobert-judul-classifier.zip')