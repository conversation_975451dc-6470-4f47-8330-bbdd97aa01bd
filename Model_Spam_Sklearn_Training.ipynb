{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Model Spam Detection dengan <PERSON>\n", "## V<PERSON><PERSON> tanpa wandb, menggunakan sklearn training patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install Libraries (Jika di Google Colab)\n", "# !pip install transformers datasets scikit-learn wordcloud matplotlib torch\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc\n", "from sklearn.metrics import roc_auc_score, precision_recall_curve\n", "from sklearn.base import BaseEstimator, ClassifierMixin\n", "from datasets import Dataset\n", "from transformers import BertTokenizer, BertForSequenceClassification\n", "import torch\n", "from torch.utils.data import DataLoader, TensorDataset\n", "from torch.optim import AdamW\n", "from torch.nn import CrossEntropyLoss\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Semua library berhasil diimport!\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load dan eksplorasi dataset\n", "print(\"=== Loading Dataset ===\")\n", "df = pd.read_csv('Dataset.csv')\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Columns: {df.columns.tolist()}\")\n", "print(\"\\nDataset info:\")\n", "df.info()\n", "\n", "print(\"\\nFirst 5 rows:\")\n", "print(df.head())\n", "\n", "print(\"\\nLabel distribution:\")\n", "print(df['Label'].value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data preprocessing\n", "import re\n", "from collections import Counter\n", "\n", "def clean_text(text):\n", "    \"\"\"Membersihkan teks dari karakter yang tidak diinginkan\"\"\"\n", "    if pd.isna(text):\n", "        return \"\"\n", "    \n", "    # Convert to string\n", "    text = str(text)\n", "    \n", "    # Remove URLs\n", "    text = re.sub(r'http\\S+|www\\S+|https\\S+', '', text, flags=re.MULTILINE)\n", "    \n", "    # Remove special characters but keep Indonesian characters\n", "    text = re.sub(r'[^a-zA-Z0-9\\s]', '', text)\n", "    \n", "    # Remove extra whitespace\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    # Convert to lowercase\n", "    text = text.lower()\n", "    \n", "    return text\n", "\n", "print(\"=== Data Preprocessing ===\")\n", "\n", "# Clean data\n", "df = df.dropna(subset=['Label'])\n", "df['comment'] = df['Comment'].apply(clean_text)\n", "df['label'] = df['Label'].astype(int)\n", "\n", "# Remove empty comments\n", "df = df[df['comment'].str.len() > 0]\n", "\n", "print(f\"Dataset shape after cleaning: {df.shape}\")\n", "print(f\"Label distribution after cleaning:\")\n", "print(df['label'].value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sklearn-style BERT Classifier\n", "class SklearnBertClassifier(BaseEstimator, ClassifierMixin):\n", "    \"\"\"BERT Classifier yang kompatibel dengan sklearn\"\"\"\n", "    \n", "    def __init__(self, model_name='indobenchmark/indobert-base-p1', \n", "                 max_length=128, learning_rate=2e-5, batch_size=16, \n", "                 num_epochs=3, patience=3, min_delta=0.001):\n", "        self.model_name = model_name\n", "        self.max_length = max_length\n", "        self.learning_rate = learning_rate\n", "        self.batch_size = batch_size\n", "        self.num_epochs = num_epochs\n", "        self.patience = patience\n", "        self.min_delta = min_delta\n", "        \n", "        # Initialize components\n", "        self.tokenizer = None\n", "        self.model = None\n", "        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "        \n", "        # Training history\n", "        self.history = {\n", "            'train_loss': [],\n", "            'val_loss': [],\n", "            'train_acc': [],\n", "            'val_acc': []\n", "        }\n", "    \n", "    def _initialize_model(self):\n", "        \"\"\"Initialize tokenizer and model\"\"\"\n", "        print(f\"Loading {self.model_name}...\")\n", "        self.tokenizer = BertTokenizer.from_pretrained(self.model_name)\n", "        self.model = BertForSequenceClassification.from_pretrained(\n", "            self.model_name, num_labels=2\n", "        )\n", "        self.model.to(self.device)\n", "        print(f\"Model loaded on {self.device}\")\n", "    \n", "    def _tokenize_data(self, texts):\n", "        \"\"\"Tokenize texts\"\"\"\n", "        encodings = self.tokenizer(\n", "            list(texts),\n", "            truncation=True,\n", "            padding=True,\n", "            max_length=self.max_length,\n", "            return_tensors='pt'\n", "        )\n", "        return encodings\n", "    \n", "    def _create_dataloader(self, encodings, labels=None, shuffle=True):\n", "        \"\"\"Create DataLoader\"\"\"\n", "        if labels is not None:\n", "            dataset = TensorDataset(\n", "                encodings['input_ids'],\n", "                encodings['attention_mask'],\n", "                torch.tensor(labels, dtype=torch.long)\n", "            )\n", "        else:\n", "            dataset = TensorDataset(\n", "                encodings['input_ids'],\n", "                encodings['attention_mask']\n", "            )\n", "        \n", "        return DataLoader(dataset, batch_size=self.batch_size, shuffle=shuffle)\n", "    \n", "    def fit(self, X, y, X_val=None, y_val=None, verbose=True):\n", "        \"\"\"Train the model (sklearn-style fit method)\"\"\"\n", "        if self.model is None:\n", "            self._initialize_model()\n", "        \n", "        # Prepare data\n", "        train_encodings = self._tokenize_data(X)\n", "        train_loader = self._create_dataloader(train_encodings, y, shuffle=True)\n", "        \n", "        val_loader = None\n", "        if X_val is not None and y_val is not None:\n", "            val_encodings = self._tokenize_data(X_val)\n", "            val_loader = self._create_dataloader(val_encodings, y_val, shuffle=False)\n", "        \n", "        # Setup optimizer and loss\n", "        optimizer = AdamW(self.model.parameters(), lr=self.learning_rate)\n", "        criterion = CrossEntropyLoss()\n", "        \n", "        # Training loop\n", "        best_val_loss = float('inf')\n", "        patience_counter = 0\n", "        \n", "        for epoch in range(self.num_epochs):\n", "            # Training phase\n", "            train_loss, train_acc = self._train_epoch(train_loader, optimizer, criterion)\n", "            self.history['train_loss'].append(train_loss)\n", "            self.history['train_acc'].append(train_acc)\n", "            \n", "            # Validation phase\n", "            if val_loader is not None:\n", "                val_loss, val_acc = self._validate_epoch(val_loader, criterion)\n", "                self.history['val_loss'].append(val_loss)\n", "                self.history['val_acc'].append(val_acc)\n", "                \n", "                if verbose:\n", "                    print(f\"Epoch {epoch+1}/{self.num_epochs}:\")\n", "                    print(f\"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}\")\n", "                    print(f\"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}\")\n", "                \n", "                # Early stopping\n", "                if val_loss < best_val_loss - self.min_delta:\n", "                    best_val_loss = val_loss\n", "                    patience_counter = 0\n", "                else:\n", "                    patience_counter += 1\n", "                    if patience_counter >= self.patience:\n", "                        print(f\"Early stopping at epoch {epoch+1}\")\n", "                        break\n", "            else:\n", "                if verbose:\n", "                    print(f\"Epoch {epoch+1}/{self.num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}\")\n", "        \n", "        return self\n", "    \n", "    def _train_epoch(self, dataloader, optimizer, criterion):\n", "        \"\"\"Train for one epoch\"\"\"\n", "        self.model.train()\n", "        total_loss = 0\n", "        correct = 0\n", "        total = 0\n", "        \n", "        for batch in dataloader:\n", "            input_ids, attention_mask, labels = [b.to(self.device) for b in batch]\n", "            \n", "            optimizer.zero_grad()\n", "            \n", "            outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)\n", "            loss = criterion(outputs.logits, labels)\n", "            \n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            total_loss += loss.item()\n", "            \n", "            _, predicted = torch.max(outputs.logits.data, 1)\n", "            total += labels.size(0)\n", "            correct += (predicted == labels).sum().item()\n", "        \n", "        return total_loss / len(dataloader), correct / total\n", "    \n", "    def _validate_epoch(self, dataloader, criterion):\n", "        \"\"\"Validate for one epoch\"\"\"\n", "        self.model.eval()\n", "        total_loss = 0\n", "        correct = 0\n", "        total = 0\n", "        \n", "        with torch.no_grad():\n", "            for batch in dataloader:\n", "                input_ids, attention_mask, labels = [b.to(self.device) for b in batch]\n", "                \n", "                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)\n", "                loss = criterion(outputs.logits, labels)\n", "                \n", "                total_loss += loss.item()\n", "                \n", "                _, predicted = torch.max(outputs.logits.data, 1)\n", "                total += labels.size(0)\n", "                correct += (predicted == labels).sum().item()\n", "        \n", "        return total_loss / len(dataloader), correct / total\n", "    \n", "    def predict(self, X):\n", "        \"\"\"Make predictions (sklearn-style predict method)\"\"\"\n", "        if self.model is None:\n", "            raise ValueError(\"Model not trained yet. Call fit() first.\")\n", "        \n", "        self.model.eval()\n", "        encodings = self._tokenize_data(X)\n", "        dataloader = self._create_dataloader(encodings, shuffle=False)\n", "        \n", "        predictions = []\n", "        \n", "        with torch.no_grad():\n", "            for batch in dataloader:\n", "                if len(batch) == 3:  # Has labels\n", "                    input_ids, attention_mask, _ = [b.to(self.device) for b in batch]\n", "                else:  # No labels\n", "                    input_ids, attention_mask = [b.to(self.device) for b in batch]\n", "                \n", "                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)\n", "                _, predicted = torch.max(outputs.logits.data, 1)\n", "                predictions.extend(predicted.cpu().numpy())\n", "        \n", "        return np.array(predictions)\n", "    \n", "    def predict_proba(self, X):\n", "        \"\"\"Predict class probabilities\"\"\"\n", "        if self.model is None:\n", "            raise ValueError(\"Model not trained yet. Call fit() first.\")\n", "        \n", "        self.model.eval()\n", "        encodings = self._tokenize_data(X)\n", "        dataloader = self._create_dataloader(encodings, shuffle=False)\n", "        \n", "        probabilities = []\n", "        \n", "        with torch.no_grad():\n", "            for batch in dataloader:\n", "                if len(batch) == 3:  # Has labels\n", "                    input_ids, attention_mask, _ = [b.to(self.device) for b in batch]\n", "                else:  # No labels\n", "                    input_ids, attention_mask = [b.to(self.device) for b in batch]\n", "                \n", "                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)\n", "                probs = torch.softmax(outputs.logits, dim=-1)\n", "                probabilities.extend(probs.cpu().numpy())\n", "        \n", "        return np.array(probabilities)\n", "    \n", "    def plot_training_history(self):\n", "        \"\"\"Plot training history\"\"\"\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))\n", "        \n", "        # Loss plot\n", "        ax1.plot(self.history['train_loss'], label='Train Loss')\n", "        if self.history['val_loss']:\n", "            ax1.plot(self.history['val_loss'], label='Val Loss')\n", "        ax1.set_title('Training Loss')\n", "        ax1.set_xlabel('Epoch')\n", "        ax1.set_ylabel('Loss')\n", "        ax1.legend()\n", "        ax1.grid(True)\n", "        \n", "        # Accuracy plot\n", "        ax2.plot(self.history['train_acc'], label='Train Acc')\n", "        if self.history['val_acc']:\n", "            ax2.plot(self.history['val_acc'], label='Val Acc')\n", "        ax2.set_title('Training Accuracy')\n", "        ax2.set_xlabel('Epoch')\n", "        ax2.set_ylabel('Accuracy')\n", "        ax2.legend()\n", "        ax2.grid(True)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "print(\"✅ SklearnBertClassifier class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data menggunakan sklearn\n", "print(\"=== Data Splitting ===\")\n", "\n", "X = df['comment'].values\n", "y = df['label'].values\n", "\n", "# Split train/test\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "# Split train/validation\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_train, y_train, test_size=0.2, random_state=42, stratify=y_train\n", ")\n", "\n", "print(f\"Training set: {len(X_train)} samples\")\n", "print(f\"Validation set: {len(X_val)} samples\")\n", "print(f\"Test set: {len(X_test)} samples\")\n", "\n", "print(\"\\nLabel distribution:\")\n", "print(f\"Train: {np.bincount(y_train)}\")\n", "print(f\"Val: {np.bincount(y_val)}\")\n", "print(f\"Test: {np.bincount(y_test)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Training dengan sklearn-style approach\n", "print(\"=== Model Training (Sklearn Style) ===\")\n", "\n", "# Initialize classifier\n", "classifier = SklearnBertClassifier(\n", "    model_name='indobenchmark/indobert-base-p1',\n", "    max_length=128,\n", "    learning_rate=2e-5,\n", "    batch_size=16,\n", "    num_epochs=3,\n", "    patience=3,\n", "    min_delta=0.001\n", ")\n", "\n", "print(\"Training configuration:\")\n", "print(f\"  Model: {classifier.model_name}\")\n", "print(f\"  Max length: {classifier.max_length}\")\n", "print(f\"  Learning rate: {classifier.learning_rate}\")\n", "print(f\"  Batch size: {classifier.batch_size}\")\n", "print(f\"  Epochs: {classifier.num_epochs}\")\n", "print(f\"  Device: {classifier.device}\")\n", "\n", "# Train the model\n", "print(\"\\nStarting training...\")\n", "classifier.fit(X_train, y_train, X_val, y_val, verbose=True)\n", "\n", "print(\"\\n✅ Training completed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training history\n", "print(\"=== Training History ===\")\n", "classifier.plot_training_history()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluation menggunakan sklearn metrics\n", "print(\"=== Model Evaluation (Sklearn Metrics) ===\")\n", "\n", "def evaluate_model(classifier, X, y, dataset_name):\n", "    \"\"\"Evaluate model using sklearn metrics\"\"\"\n", "    print(f\"\\n=== {dataset_name} Evaluation ===\")\n", "    \n", "    # Predictions\n", "    y_pred = classifier.predict(X)\n", "    y_prob = classifier.predict_proba(X)[:, 1]  # Probability for class 1\n", "    \n", "    # Calculate metrics\n", "    accuracy = accuracy_score(y, y_pred)\n", "    precision = precision_score(y, y_pred, average='weighted')\n", "    recall = recall_score(y, y_pred, average='weighted')\n", "    f1 = f1_score(y, y_pred, average='weighted')\n", "    auc_score = roc_auc_score(y, y_prob)\n", "    \n", "    print(f\"Accuracy:  {accuracy:.4f} ({accuracy*100:.2f}%)\")\n", "    print(f\"Precision: {precision:.4f} ({precision*100:.2f}%)\")\n", "    print(f\"Recall:    {recall:.4f} ({recall*100:.2f}%)\")\n", "    print(f\"F1-Score:  {f1:.4f} ({f1*100:.2f}%)\")\n", "    print(f\"AUC Score: {auc_score:.4f} ({auc_score*100:.2f}%)\")\n", "    \n", "    # Classification report\n", "    print(f\"\\nClassification Report ({dataset_name}):\")\n", "    print(classification_report(y, y_pred, target_names=['Non-Spam', 'Spam']))\n", "    \n", "    return {\n", "        'accuracy': accuracy,\n", "        'precision': precision,\n", "        'recall': recall,\n", "        'f1_score': f1,\n", "        'auc_score': auc_score,\n", "        'y_pred': y_pred,\n", "        'y_prob': y_prob\n", "    }\n", "\n", "# Evaluate on all sets\n", "train_metrics = evaluate_model(classifier, X_train, y_train, \"Training Set\")\n", "val_metrics = evaluate_model(classifier, X_val, y_val, \"Validation Set\")\n", "test_metrics = evaluate_model(classifier, X_test, y_test, \"Test Set\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix Analysis\n", "print(\"=== Confusion Matrix Analysis ===\")\n", "\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "datasets = [\n", "    (y_train, train_metrics['y_pred'], \"Training Set\"),\n", "    (y_val, val_metrics['y_pred'], \"Validation Set\"),\n", "    (y_test, test_metrics['y_pred'], \"Test Set\")\n", "]\n", "\n", "for i, (y_true, y_pred, title) in enumerate(datasets):\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=['Non-Spam', 'Spam'], \n", "                yticklabels=['Non-Spam', 'Spam'],\n", "                ax=axes[i])\n", "    axes[i].set_title(f'Confusion Matrix - {title}')\n", "    axes[i].set_xlabel('Predicted Label')\n", "    axes[i].set_ylabel('True Label')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ROC Curve Analysis\n", "print(\"=== ROC Curve Analysis ===\")\n", "\n", "plt.figure(figsize=(12, 4))\n", "\n", "# ROC Curves\n", "plt.subplot(1, 2, 1)\n", "datasets_roc = [\n", "    (y_train, train_metrics['y_prob'], \"Training\", 'blue'),\n", "    (y_val, val_metrics['y_prob'], \"Validation\", 'red'),\n", "    (y_test, test_metrics['y_prob'], \"Test\", 'green')\n", "]\n", "\n", "for y_true, y_prob, label, color in datasets_roc:\n", "    fpr, tpr, _ = roc_curve(y_true, y_prob)\n", "    auc_score = roc_auc_score(y_true, y_prob)\n", "    plt.plot(fpr, tpr, color=color, label=f'{label} (AUC = {auc_score:.3f})')\n", "\n", "plt.plot([0, 1], [0, 1], 'k--', label='Random')\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title('ROC Curves')\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "# Precision-<PERSON><PERSON><PERSON>\n", "plt.subplot(1, 2, 2)\n", "for y_true, y_prob, label, color in datasets_roc:\n", "    precision, recall, _ = precision_recall_curve(y_true, y_prob)\n", "    plt.plot(recall, precision, color=color, label=f'{label}')\n", "\n", "plt.xlabel('Recall')\n", "plt.ylabel('Precision')\n", "plt.title('Precision-<PERSON><PERSON><PERSON>urves')\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cross-validation menggunakan sklearn\n", "print(\"=== Cross-Validation (Sklearn Style) ===\")\n", "\n", "# Note: Cross-validation dengan BERT memerlukan waktu lama\n", "# Untuk demonstrasi, kita akan menggunakan subset data\n", "\n", "# Simplified cross-validation dengan subset data\n", "subset_size = min(1000, len(X_train))  # <PERSON>akan maksimal 1000 samples\n", "X_subset = X_train[:subset_size]\n", "y_subset = y_train[:subset_size]\n", "\n", "print(f\"Performing cross-validation on {subset_size} samples...\")\n", "\n", "# Manual cross-validation (karena BERT training kompleks)\n", "cv_scores = []\n", "kfold = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)\n", "\n", "for fold, (train_idx, val_idx) in enumerate(kfold.split(X_subset, y_subset)):\n", "    print(f\"\\nFold {fold + 1}/3\")\n", "    \n", "    X_cv_train, X_cv_val = X_subset[train_idx], X_subset[val_idx]\n", "    y_cv_train, y_cv_val = y_subset[train_idx], y_subset[val_idx]\n", "    \n", "    # Train a new model for this fold\n", "    cv_classifier = SklearnBertClassifier(\n", "        model_name='indobenchmark/indobert-base-p1',\n", "        max_length=128,\n", "        learning_rate=2e-5,\n", "        batch_size=8,  # Smaller batch for CV\n", "        num_epochs=2,  # Fewer epochs for CV\n", "        patience=2\n", "    )\n", "    \n", "    cv_classifier.fit(X_cv_train, y_cv_train, X_cv_val, y_cv_val, verbose=False)\n", "    \n", "    # Evaluate\n", "    y_cv_pred = cv_classifier.predict(X_cv_val)\n", "    cv_score = accuracy_score(y_cv_val, y_cv_pred)\n", "    cv_scores.append(cv_score)\n", "    \n", "    print(f\"Fold {fold + 1} Accuracy: {cv_score:.4f}\")\n", "\n", "cv_mean = np.mean(cv_scores)\n", "cv_std = np.std(cv_scores)\n", "\n", "print(f\"\\n=== Cross-Validation Results ===\")\n", "print(f\"CV Scores: {[f'{score:.4f}' for score in cv_scores]}\")\n", "print(f\"Mean CV Score: {cv_mean:.4f} (+/- {cv_std*2:.4f})\")\n", "print(f\"CV Standard Deviation: {cv_std:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model Performance Summary\n", "print(\"=== COMPREHENSIVE MODEL PERFORMANCE SUMMARY ===\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n1. ACCURACY COMPARISON:\")\n", "print(\"-\" * 30)\n", "print(f\"Training Accuracy:   {train_metrics['accuracy']:.4f} ({train_metrics['accuracy']*100:.2f}%)\")\n", "print(f\"Validation Accuracy: {val_metrics['accuracy']:.4f} ({val_metrics['accuracy']*100:.2f}%)\")\n", "print(f\"Test Accuracy:       {test_metrics['accuracy']:.4f} ({test_metrics['accuracy']*100:.2f}%)\")\n", "\n", "print(\"\\n2. F1-SCORE COMPARISON:\")\n", "print(\"-\" * 30)\n", "print(f\"Training F1:   {train_metrics['f1_score']:.4f}\")\n", "print(f\"Validation F1: {val_metrics['f1_score']:.4f}\")\n", "print(f\"Test F1:       {test_metrics['f1_score']:.4f}\")\n", "\n", "print(\"\\n3. AUC SCORE COMPARISON:\")\n", "print(\"-\" * 30)\n", "print(f\"Training AUC:   {train_metrics['auc_score']:.4f}\")\n", "print(f\"Validation AUC: {val_metrics['auc_score']:.4f}\")\n", "print(f\"Test AUC:       {test_metrics['auc_score']:.4f}\")\n", "\n", "print(\"\\n4. CROSS-VALIDATION:\")\n", "print(\"-\" * 30)\n", "print(f\"CV Mean Score: {cv_mean:.4f} (+/- {cv_std*2:.4f})\")\n", "\n", "# Overfitting Analysis\n", "train_val_gap = abs(train_metrics['accuracy'] - val_metrics['accuracy'])\n", "val_test_gap = abs(val_metrics['accuracy'] - test_metrics['accuracy'])\n", "\n", "print(\"\\n5. OVERFITTING ANALYSIS:\")\n", "print(\"-\" * 30)\n", "print(f\"Train-Val Gap:  {train_val_gap:.4f} ({train_val_gap*100:.2f}%)\")\n", "print(f\"Val-Test Gap:   {val_test_gap:.4f} ({val_test_gap*100:.2f}%)\")\n", "\n", "if train_val_gap < 0.05:\n", "    overfitting_status = \"✅ No significant overfitting\"\n", "elif train_val_gap < 0.10:\n", "    overfitting_status = \"⚠️ Mild overfitting\"\n", "else:\n", "    overfitting_status = \"❌ Significant overfitting\"\n", "\n", "print(f\"Status: {overfitting_status}\")\n", "\n", "print(\"\\n6. MODEL CONFIGURATION:\")\n", "print(\"-\" * 30)\n", "print(f\"Model: {classifier.model_name}\")\n", "print(f\"Max Length: {classifier.max_length}\")\n", "print(f\"Learning Rate: {classifier.learning_rate}\")\n", "print(f\"Batch Size: {classifier.batch_size}\")\n", "print(f\"Epochs Trained: {len(classifier.history['train_loss'])}\")\n", "print(f\"Device: {classifier.device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save Model (Sklearn Style)\n", "print(\"=== Model Saving ===\")\n", "\n", "import os\n", "import pickle\n", "import torch\n", "\n", "# Create save directory\n", "save_dir = './sklearn_bert_classifier'\n", "os.makedirs(save_dir, exist_ok=True)\n", "\n", "# Save the PyTorch model and tokenizer\n", "model_path = os.path.join(save_dir, 'pytorch_model')\n", "classifier.model.save_pretrained(model_path)\n", "classifier.tokenizer.save_pretrained(model_path)\n", "\n", "# Save the classifier configuration and history\n", "classifier_config = {\n", "    'model_name': classifier.model_name,\n", "    'max_length': classifier.max_length,\n", "    'learning_rate': classifier.learning_rate,\n", "    'batch_size': classifier.batch_size,\n", "    'num_epochs': classifier.num_epochs,\n", "    'patience': classifier.patience,\n", "    'min_delta': classifier.min_delta,\n", "    'history': classifier.history\n", "}\n", "\n", "config_path = os.path.join(save_dir, 'classifier_config.pkl')\n", "with open(config_path, 'wb') as f:\n", "    pickle.dump(classifier_config, f)\n", "\n", "# Save evaluation results\n", "results = {\n", "    'train_metrics': train_metrics,\n", "    'val_metrics': val_metrics,\n", "    'test_metrics': test_metrics,\n", "    'cv_scores': cv_scores,\n", "    'cv_mean': cv_mean,\n", "    'cv_std': cv_std\n", "}\n", "\n", "results_path = os.path.join(save_dir, 'evaluation_results.pkl')\n", "with open(results_path, 'wb') as f:\n", "    pickle.dump(results, f)\n", "\n", "print(f\"✅ Model saved to: {save_dir}\")\n", "print(f\"   - PyTorch model: {model_path}\")\n", "print(f\"   - Configuration: {config_path}\")\n", "print(f\"   - Results: {results_path}\")\n", "\n", "# Function to load the model later\n", "def load_sklearn_bert_classifier(save_dir):\n", "    \"\"\"Load the saved sklearn BERT classifier\"\"\"\n", "    import pickle\n", "    from transformers import BertTokenizer, BertForSequenceClassification\n", "    \n", "    # Load configuration\n", "    config_path = os.path.join(save_dir, 'classifier_config.pkl')\n", "    with open(config_path, 'rb') as f:\n", "        config = pickle.load(f)\n", "    \n", "    # Create classifier instance\n", "    classifier = SklearnBertClassifier(**{k: v for k, v in config.items() if k != 'history'})\n", "    \n", "    # Load model and tokenizer\n", "    model_path = os.path.join(save_dir, 'pytorch_model')\n", "    classifier.tokenizer = BertTokenizer.from_pretrained(model_path)\n", "    classifier.model = BertForSequenceClassification.from_pretrained(model_path)\n", "    classifier.model.to(classifier.device)\n", "    \n", "    # Restore history\n", "    classifier.history = config['history']\n", "    \n", "    return classifier\n", "\n", "print(\"\\n💡 To load the model later, use:\")\n", "print(f\"   classifier = load_sklearn_bert_classifier('{save_dir}')\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final Summary and Recommendations\n", "print(\"=== FINAL SUMMARY AND RECOMMENDATIONS ===\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n🎯 MODEL PERFORMANCE:\")\n", "print(f\"   • Test Accuracy: {test_metrics['accuracy']*100:.2f}%\")\n", "print(f\"   • Test F1-Score: {test_metrics['f1_score']:.4f}\")\n", "print(f\"   • Test AUC: {test_metrics['auc_score']:.4f}\")\n", "print(f\"   • Cross-Validation: {cv_mean:.4f} (+/- {cv_std*2:.4f})\")\n", "\n", "print(\"\\n✅ ADVANTAGES OF SKLEARN APPROACH:\")\n", "print(\"   • No dependency on wandb or external logging\")\n", "print(\"   • Compatible with sklearn ecosystem\")\n", "print(\"   • Easy to integrate with sklearn pipelines\")\n", "print(\"   • Standard fit/predict interface\")\n", "print(\"   • Built-in early stopping and validation\")\n", "print(\"   • Comprehensive evaluation metrics\")\n", "print(\"   • Easy model saving and loading\")\n", "\n", "print(\"\\n🔧 SKLEARN FEATURES IMPLEMENTED:\")\n", "print(\"   • BaseEstimator and ClassifierMixin inheritance\")\n", "print(\"   • Standard fit() and predict() methods\")\n", "print(\"   • predict_proba() for probability estimates\")\n", "print(\"   • Compatible with sklearn metrics\")\n", "print(\"   • Cross-validation support\")\n", "print(\"   • Stratified train/test splits\")\n", "\n", "print(\"\\n📊 EVALUATION METHODS USED:\")\n", "print(\"   • Accuracy, Precision, Recall, F1-Score\")\n", "print(\"   • ROC-AUC and Precision-Recall curves\")\n", "print(\"   • Confusion Matrix analysis\")\n", "print(\"   • Cross-validation with StratifiedKFold\")\n", "print(\"   • Overfitting detection\")\n", "print(\"   • Training history visualization\")\n", "\n", "print(\"\\n🚀 NEXT STEPS:\")\n", "print(\"   1. Fine-tune hyperparameters if needed\")\n", "print(\"   2. Experiment with different BERT models\")\n", "print(\"   3. Add more sophisticated preprocessing\")\n", "print(\"   4. Implement ensemble methods\")\n", "print(\"   5. Deploy model for production use\")\n", "\n", "print(\"\\n💡 USAGE EXAMPLE:\")\n", "print(\"   # Load saved model\")\n", "print(f\"   classifier = load_sklearn_bert_classifier('{save_dir}')\")\n", "print(\"   \")\n", "print(\"   # Make predictions\")\n", "print(\"   predictions = classifier.predict(['Sample text here'])\")\n", "print(\"   probabilities = classifier.predict_proba(['Sample text here'])\")\n", "\n", "print(\"\\n🎉 <PERSON>LEARN-STYLE BERT TRAINING COMPLETED!\")\n", "print(\"   No wandb dependency required!\")\n", "print(\"   Pure sklearn + PyTorch implementation!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}