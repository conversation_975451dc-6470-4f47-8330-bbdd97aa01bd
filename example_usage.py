"""
Example usage of SklearnBertClassifier
Demonstrates how to replace wandb with sklearn training
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn_bert_classifier import SklearnBertClassifier, evaluate_model
import re


def clean_text(text):
    """Membersihkan teks dari karakter yang tidak diinginkan"""
    if pd.isna(text):
        return ""
    
    # Convert to string
    text = str(text)
    
    # Remove URLs
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    
    # Remove special characters but keep Indonesian characters
    text = re.sub(r'[^a-zA-Z0-9\s]', '', text)
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    # Convert to lowercase
    text = text.lower()
    
    return text


def main():
    print("=== Sklearn BERT Classifier Example ===")
    print("No wandb dependency required!")
    
    # Load and preprocess data
    print("\n1. Loading and preprocessing data...")
    df = pd.read_csv('Dataset.csv')
    
    # Clean data
    df = df.dropna(subset=['Label'])
    df['comment'] = df['Comment'].apply(clean_text)
    df['label'] = df['Label'].astype(int)
    df = df[df['comment'].str.len() > 0]
    
    print(f"Dataset shape: {df.shape}")
    print(f"Label distribution: {df['label'].value_counts().to_dict()}")
    
    # Prepare data
    X = df['comment'].values
    y = df['label'].values
    
    # Split data
    print("\n2. Splitting data...")
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"Training set: {len(X_train)} samples")
    print(f"Validation set: {len(X_val)} samples")
    print(f"Test set: {len(X_test)} samples")
    
    # Initialize and train classifier
    print("\n3. Training sklearn-style BERT classifier...")
    classifier = SklearnBertClassifier(
        model_name='indobenchmark/indobert-base-p1',
        max_length=128,
        learning_rate=2e-5,
        batch_size=16,
        num_epochs=3,
        patience=3,
        min_delta=0.001
    )
    
    # Train the model (sklearn-style fit)
    classifier.fit(X_train, y_train, X_val, y_val, verbose=True)
    
    print("\n✅ Training completed!")
    
    # Evaluate model
    print("\n4. Evaluating model...")
    train_metrics = evaluate_model(classifier, X_train, y_train, "Training Set")
    val_metrics = evaluate_model(classifier, X_val, y_val, "Validation Set")
    test_metrics = evaluate_model(classifier, X_test, y_test, "Test Set")
    
    # Plot training history
    print("\n5. Plotting training history...")
    classifier.plot_training_history()
    
    # Example predictions
    print("\n6. Example predictions...")
    sample_texts = [
        "Klik link ini untuk hadiah gratis!",
        "Terima kasih atas informasinya",
        "MENANG JUTAAN RUPIAH SEKARANG JUGA!!!",
        "Artikel yang sangat bermanfaat"
    ]
    
    predictions = classifier.predict(sample_texts)
    probabilities = classifier.predict_proba(sample_texts)
    
    print("\nSample predictions:")
    for i, (text, pred, prob) in enumerate(zip(sample_texts, predictions, probabilities)):
        label = "SPAM" if pred == 1 else "NON-SPAM"
        confidence = prob[pred] * 100
        print(f"{i+1}. '{text[:50]}...' -> {label} ({confidence:.1f}%)")
    
    # Summary
    print("\n=== SUMMARY ===")
    print(f"✅ Model trained successfully without wandb!")
    print(f"📊 Test Accuracy: {test_metrics['accuracy']*100:.2f}%")
    print(f"📊 Test F1-Score: {test_metrics['f1_score']:.4f}")
    print(f"📊 Test AUC: {test_metrics['auc_score']:.4f}")
    
    print("\n🔧 Sklearn features used:")
    print("   • BaseEstimator and ClassifierMixin inheritance")
    print("   • Standard fit() and predict() methods")
    print("   • predict_proba() for probability estimates")
    print("   • Compatible with sklearn metrics")
    print("   • Early stopping and validation")
    
    print("\n💡 No external dependencies:")
    print("   • No wandb required")
    print("   • No external logging services")
    print("   • Pure sklearn + PyTorch implementation")
    
    return classifier, test_metrics


if __name__ == "__main__":
    try:
        classifier, metrics = main()
        print("\n🎉 Example completed successfully!")
    except FileNotFoundError:
        print("❌ Error: Dataset.csv not found!")
        print("Please make sure Dataset.csv is in the current directory.")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("Please check your environment and dependencies.")
